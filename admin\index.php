<?php
require_once '../config/config.php';

// Check if user is admin or editor
if (!isEditor()) {
    redirectTo(SITE_URL . '/login.php');
}

$pageTitle = 'لوحة التحكم';

// Get dashboard statistics
try {
    // Total articles
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM articles");
    $stmt->execute();
    $totalArticles = $stmt->fetch()['total'];
    
    // Published articles
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM articles WHERE status = 'published'");
    $stmt->execute();
    $publishedArticles = $stmt->fetch()['total'];
    
    // Total views
    $stmt = $pdo->prepare("SELECT SUM(views) as total FROM articles");
    $stmt->execute();
    $totalViews = $stmt->fetch()['total'] ?: 0;
    
    // Total comments
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM comments");
    $stmt->execute();
    $totalComments = $stmt->fetch()['total'];
    
    // Pending comments
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM comments WHERE status = 'pending'");
    $stmt->execute();
    $pendingComments = $stmt->fetch()['total'];
    
    // Recent articles
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, u.username as author_name
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN users u ON a.author_id = u.id 
        ORDER BY a.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recentArticles = $stmt->fetchAll();
    
    // Popular articles
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        WHERE a.status = 'published'
        ORDER BY a.views DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $popularArticles = $stmt->fetchAll();
    
    // Recent comments
    $stmt = $pdo->prepare("
        SELECT c.*, a.title as article_title, a.slug as article_slug
        FROM comments c 
        LEFT JOIN articles a ON c.article_id = a.id 
        ORDER BY c.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $recentComments = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $totalArticles = $publishedArticles = $totalViews = $totalComments = $pendingComments = 0;
    $recentArticles = $popularArticles = $recentComments = [];
}

include 'includes/header.php';
?>

<div class="dashboard-content">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card stat-primary">
                <div class="stat-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-number"><?php echo formatNumber($totalArticles); ?></h3>
                    <p class="stat-label">إجمالي المقالات</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card stat-success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-number"><?php echo formatNumber($publishedArticles); ?></h3>
                    <p class="stat-label">المقالات المنشورة</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card stat-info">
                <div class="stat-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-number"><?php echo formatNumber($totalViews); ?></h3>
                    <p class="stat-label">إجمالي المشاهدات</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card stat-warning">
                <div class="stat-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-number"><?php echo formatNumber($totalComments); ?></h3>
                    <p class="stat-label">التعليقات</p>
                    <?php if ($pendingComments > 0): ?>
                        <small class="text-danger"><?php echo $pendingComments; ?> في الانتظار</small>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="quick-actions">
                <h5 class="mb-3">إجراءات سريعة</h5>
                <div class="action-buttons">
                    <a href="articles/create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        مقال جديد
                    </a>
                    <a href="rss/manage.php" class="btn btn-info">
                        <i class="fas fa-rss"></i>
                        إدارة RSS
                    </a>
                    <a href="comments/manage.php" class="btn btn-warning">
                        <i class="fas fa-comments"></i>
                        إدارة التعليقات
                        <?php if ($pendingComments > 0): ?>
                            <span class="badge bg-danger"><?php echo $pendingComments; ?></span>
                        <?php endif; ?>
                    </a>
                    <a href="settings.php" class="btn btn-secondary">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Articles -->
        <div class="col-lg-8 mb-4">
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-newspaper"></i>
                        آخر المقالات
                    </h5>
                    <a href="articles/" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="widget-content">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>القسم</th>
                                    <th>الكاتب</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>المشاهدات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentArticles as $article): ?>
                                <tr>
                                    <td>
                                        <div class="article-title-cell">
                                            <?php echo truncateText($article['title'], 50); ?>
                                            <?php if ($article['is_breaking']): ?>
                                                <span class="badge bg-danger">عاجل</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?php echo $article['category_name']; ?></td>
                                    <td><?php echo $article['author_name']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $article['status'] === 'published' ? 'success' : ($article['status'] === 'draft' ? 'warning' : 'secondary'); ?>">
                                            <?php 
                                            echo $article['status'] === 'published' ? 'منشور' : 
                                                ($article['status'] === 'draft' ? 'مسودة' : 'مؤرشف'); 
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo timeAgo($article['created_at']); ?></td>
                                    <td><?php echo formatNumber($article['views']); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="articles/edit.php?id=<?php echo $article['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary" title="تحرير">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>" 
                                               class="btn btn-sm btn-outline-info" title="عرض" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Widgets -->
        <div class="col-lg-4">
            <!-- Popular Articles -->
            <div class="dashboard-widget mb-4">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-fire"></i>
                        الأكثر قراءة
                    </h5>
                </div>
                <div class="widget-content">
                    <ul class="popular-list">
                        <?php foreach ($popularArticles as $index => $article): ?>
                        <li class="popular-item">
                            <div class="popular-rank"><?php echo $index + 1; ?></div>
                            <div class="popular-content">
                                <h6 class="popular-title">
                                    <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>" target="_blank">
                                        <?php echo truncateText($article['title'], 60); ?>
                                    </a>
                                </h6>
                                <div class="popular-meta">
                                    <span><?php echo $article['category_name']; ?></span>
                                    <span><?php echo formatNumber($article['views']); ?> مشاهدة</span>
                                </div>
                            </div>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>

            <!-- Recent Comments -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-comments"></i>
                        آخر التعليقات
                    </h5>
                    <a href="comments/" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="widget-content">
                    <ul class="comments-list">
                        <?php foreach ($recentComments as $comment): ?>
                        <li class="comment-item">
                            <div class="comment-author">
                                <i class="fas fa-user-circle"></i>
                                <?php echo $comment['author_name'] ?: 'زائر'; ?>
                            </div>
                            <div class="comment-content">
                                <?php echo truncateText($comment['content'], 80); ?>
                            </div>
                            <div class="comment-meta">
                                <span>على: <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $comment['article_slug']; ?>" target="_blank">
                                    <?php echo truncateText($comment['article_title'], 30); ?>
                                </a></span>
                                <span><?php echo timeAgo($comment['created_at']); ?></span>
                            </div>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-content {
    padding: 20px 0;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-primary .stat-icon { background: var(--primary-color); }
.stat-success .stat-icon { background: var(--accent-color); }
.stat-info .stat-icon { background: #0ea5e9; }
.stat-warning .stat-icon { background: #f59e0b; }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-color);
}

.stat-label {
    margin: 0;
    color: var(--text-light);
    font-size: 0.9rem;
}

.quick-actions {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow);
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.dashboard-widget {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.widget-header {
    background: var(--light-color);
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-title {
    margin: 0;
    color: var(--text-color);
    font-weight: 600;
}

.widget-content {
    padding: 0;
}

.table th {
    background: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--text-color);
    padding: 15px;
}

.table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.article-title-cell {
    max-width: 200px;
}

.popular-list, .comments-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.popular-item, .comment-item {
    padding: 15px 25px;
    border-bottom: 1px solid var(--border-color);
}

.popular-item:last-child, .comment-item:last-child {
    border-bottom: none;
}

.popular-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.popular-rank {
    background: var(--primary-color);
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    flex-shrink: 0;
}

.popular-title {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.popular-title a {
    color: var(--text-color);
    text-decoration: none;
}

.popular-title a:hover {
    color: var(--primary-color);
}

.popular-meta {
    font-size: 0.8rem;
    color: var(--text-light);
}

.popular-meta span {
    margin-left: 10px;
}

.comment-author {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.comment-content {
    color: var(--text-color);
    margin-bottom: 8px;
    line-height: 1.4;
}

.comment-meta {
    font-size: 0.8rem;
    color: var(--text-light);
}

.comment-meta a {
    color: var(--primary-color);
    text-decoration: none;
}

@media (max-width: 768px) {
    .action-buttons {
        justify-content: center;
    }
    
    .widget-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
