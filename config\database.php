<?php
// Database configuration
class Database {
    private $host = 'localhost';
    private $db_name = 'arabic_news';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function connect() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                )
            );
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
        
        return $this->conn;
    }
}

// Create database and tables if they don't exist
function createDatabase() {
    try {
        $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS arabic_news CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE arabic_news");
        
        // Create users table
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'editor', 'user') DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // Create categories table
        $pdo->exec("CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Create tags table
        $pdo->exec("CREATE TABLE IF NOT EXISTS tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            slug VARCHAR(50) UNIQUE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Create articles table
        $pdo->exec("CREATE TABLE IF NOT EXISTS articles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            content LONGTEXT NOT NULL,
            excerpt TEXT,
            featured_image VARCHAR(255),
            category_id INT,
            author_id INT,
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            is_breaking BOOLEAN DEFAULT FALSE,
            views INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            published_at TIMESTAMP NULL,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
            FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
        )");
        
        // Create article_tags table
        $pdo->exec("CREATE TABLE IF NOT EXISTS article_tags (
            article_id INT,
            tag_id INT,
            PRIMARY KEY (article_id, tag_id),
            FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
            FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
        )");
        
        // Create comments table
        $pdo->exec("CREATE TABLE IF NOT EXISTS comments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            article_id INT NOT NULL,
            user_id INT,
            author_name VARCHAR(100),
            author_email VARCHAR(100),
            content TEXT NOT NULL,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )");
        
        // Create rss_feeds table
        $pdo->exec("CREATE TABLE IF NOT EXISTS rss_feeds (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            url VARCHAR(255) NOT NULL,
            category_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            last_fetched TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )");
        
        // Create matches table
        $pdo->exec("CREATE TABLE IF NOT EXISTS matches (
            id INT AUTO_INCREMENT PRIMARY KEY,
            home_team VARCHAR(100) NOT NULL,
            away_team VARCHAR(100) NOT NULL,
            home_score INT DEFAULT NULL,
            away_score INT DEFAULT NULL,
            match_date DATETIME NOT NULL,
            league VARCHAR(100),
            status ENUM('scheduled', 'live', 'finished') DEFAULT 'scheduled',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Create settings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // Insert default admin user
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("INSERT IGNORE INTO users (username, email, password, role) VALUES 
                   ('admin', '<EMAIL>', '$adminPassword', 'admin')");
        
        // Insert default categories
        $pdo->exec("INSERT IGNORE INTO categories (name, slug, description) VALUES 
                   ('أخبار عامة', 'general-news', 'الأخبار العامة والمتنوعة'),
                   ('رياضة', 'sports', 'الأخبار الرياضية'),
                   ('تكنولوجيا', 'technology', 'أخبار التكنولوجيا والتقنية'),
                   ('اقتصاد', 'economy', 'الأخبار الاقتصادية'),
                   ('سياسة', 'politics', 'الأخبار السياسية')");
        
        // Insert default settings
        $pdo->exec("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES 
                   ('site_name', 'الموقع الإخباري العربي'),
                   ('site_description', 'موقع إخباري عربي شامل'),
                   ('site_logo', ''),
                   ('breaking_news_enabled', '1'),
                   ('comments_enabled', '1'),
                   ('rss_update_interval', '30')");
        
        return true;
    } catch(PDOException $e) {
        echo "Database Error: " . $e->getMessage();
        return false;
    }
}

// Initialize database only if called directly
if (basename($_SERVER['PHP_SELF']) === 'database.php') {
    createDatabase();
}
?>
