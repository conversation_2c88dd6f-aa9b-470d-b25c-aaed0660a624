<?php
/**
 * Debug Script - Enable detailed error reporting
 * Access this file first to see detailed PHP errors
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// Set memory limit
ini_set('memory_limit', '256M');

// Set execution time
ini_set('max_execution_time', 300);

echo "<h1>PHP Debug Information</h1>";

// Check PHP version
echo "<h2>PHP Version</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Required: 7.4 or higher<br>";
if (version_compare(phpversion(), '7.4.0', '<')) {
    echo "<span style='color: red;'>❌ PHP version is too old!</span><br>";
} else {
    echo "<span style='color: green;'>✅ PHP version is compatible</span><br>";
}

// Check required extensions
echo "<h2>Required PHP Extensions</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'mbstring', 'curl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span style='color: green;'>✅ $ext</span><br>";
    } else {
        echo "<span style='color: red;'>❌ $ext (MISSING)</span><br>";
    }
}

// Check file permissions
echo "<h2>File Permissions</h2>";
$directories = [
    'config' => 'config/',
    'uploads' => 'uploads/',
    'uploads/images' => 'uploads/images/',
    'assets' => 'assets/',
    'admin' => 'admin/'
];

foreach ($directories as $name => $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        if (is_writable($dir)) {
            echo "<span style='color: green;'>✅ $name ($perms) - Writable</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠️ $name ($perms) - Not writable</span><br>";
        }
    } else {
        echo "<span style='color: red;'>❌ $name - Directory doesn't exist</span><br>";
    }
}

// Check critical files
echo "<h2>Critical Files</h2>";
$files = [
    'config/config.php',
    'config/database.php',
    'includes/header.php',
    'includes/footer.php',
    'index.php',
    'install.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✅ $file</span><br>";
    } else {
        echo "<span style='color: red;'>❌ $file (MISSING)</span><br>";
    }
}

// Test basic PHP syntax
echo "<h2>PHP Syntax Check</h2>";
$php_files = glob('*.php');
foreach ($php_files as $file) {
    $output = [];
    $return_var = 0;
    exec("php -l \"$file\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<span style='color: green;'>✅ $file - Syntax OK</span><br>";
    } else {
        echo "<span style='color: red;'>❌ $file - Syntax Error:</span><br>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
}

// Test database connection (if config exists)
echo "<h2>Database Connection Test</h2>";
if (file_exists('config/database.php')) {
    try {
        // Temporarily suppress errors for this test
        $old_error_reporting = error_reporting(0);
        
        include_once 'config/database.php';
        
        // Restore error reporting
        error_reporting($old_error_reporting);
        
        if (class_exists('Database')) {
            $database = new Database();
            $pdo = $database->connect();
            
            if ($pdo) {
                echo "<span style='color: green;'>✅ Database connection successful</span><br>";
                
                // Test if tables exist
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (count($tables) > 0) {
                    echo "<span style='color: green;'>✅ Database tables exist (" . count($tables) . " tables)</span><br>";
                } else {
                    echo "<span style='color: orange;'>⚠️ Database is empty - run installation</span><br>";
                }
            } else {
                echo "<span style='color: red;'>❌ Database connection failed</span><br>";
            }
        } else {
            echo "<span style='color: red;'>❌ Database class not found</span><br>";
        }
    } catch (Exception $e) {
        echo "<span style='color: red;'>❌ Database error: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span style='color: red;'>❌ Database config file missing</span><br>";
}

// Check .htaccess
echo "<h2>.htaccess File</h2>";
if (file_exists('.htaccess')) {
    echo "<span style='color: green;'>✅ .htaccess exists</span><br>";
    
    // Check if mod_rewrite is enabled
    if (function_exists('apache_get_modules')) {
        $modules = apache_get_modules();
        if (in_array('mod_rewrite', $modules)) {
            echo "<span style='color: green;'>✅ mod_rewrite is enabled</span><br>";
        } else {
            echo "<span style='color: red;'>❌ mod_rewrite is not enabled</span><br>";
        }
    } else {
        echo "<span style='color: orange;'>⚠️ Cannot check mod_rewrite status</span><br>";
    }
} else {
    echo "<span style='color: orange;'>⚠️ .htaccess file missing</span><br>";
}

// Memory and execution info
echo "<h2>PHP Configuration</h2>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";

// Server info
echo "<h2>Server Information</h2>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Directory: " . getcwd() . "<br>";

echo "<h2>Next Steps</h2>";
echo "<p>1. Fix any red ❌ issues shown above</p>";
echo "<p>2. If all checks pass, try accessing <a href='install.php'>install.php</a></p>";
echo "<p>3. If install.php works, proceed with installation</p>";
echo "<p>4. If issues persist, check the detailed error logs</p>";

// Show recent error log entries if available
echo "<h2>Recent PHP Errors</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $errors = file($error_log);
    $recent_errors = array_slice($errors, -10); // Last 10 errors
    
    if (!empty($recent_errors)) {
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        foreach ($recent_errors as $error) {
            echo htmlspecialchars($error);
        }
        echo "</pre>";
    } else {
        echo "<p>No recent errors in log file.</p>";
    }
} else {
    echo "<p>Error log file not found or not configured.</p>";
}

?>
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>
