<?php
/**
 * Session Configuration Test
 * This page tests if the session warnings are fixed and session is working properly
 */

// Enable error reporting to catch any warnings
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Session Configuration Test</h1>";

// Include config (this should not produce warnings now)
echo "<h2>Step 1: Loading Configuration</h2>";
echo "<p>Loading config.php...</p>";

require_once 'config/config.php';

echo "<p>✅ Configuration loaded without warnings!</p>";

// Test session status
echo "<h2>Step 2: Session Status</h2>";
echo "<p>Session Status: " . session_status() . " (2 = Active)</p>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session Name: " . session_name() . "</p>";

// Test SessionManager
echo "<h2>Step 3: SessionManager Test</h2>";
echo "<p>SessionManager Active: " . (SessionManager::isActive() ? 'Yes' : 'No') . "</p>";
echo "<p>User Logged In: " . (SessionManager::isLoggedIn() ? 'Yes' : 'No') . "</p>";

if (SessionManager::isLoggedIn()) {
    echo "<p>User ID: " . SessionManager::getUserId() . "</p>";
    echo "<p>Username: " . SessionManager::getUsername() . "</p>";
    echo "<p>User Role: " . SessionManager::getUserRole() . "</p>";
}

// Test CSRF Token
echo "<h2>Step 4: CSRF Token Test</h2>";
$csrfToken = generateCSRFToken();
echo "<p>CSRF Token Generated: " . substr($csrfToken, 0, 20) . "...</p>";
echo "<p>Token Verification: " . (verifyCSRFToken($csrfToken) ? 'Valid' : 'Invalid') . "</p>";

// Test Flash Messages
echo "<h2>Step 5: Flash Messages Test</h2>";
SessionManager::flash('test_message', 'This is a test flash message!');
echo "<p>Flash Message Set: 'This is a test flash message!'</p>";

$flashMessage = SessionManager::flash('test_message');
echo "<p>Flash Message Retrieved: " . ($flashMessage ?: 'None') . "</p>";

// Test session info
echo "<h2>Step 6: Session Information</h2>";
$sessionInfo = SessionManager::getInfo();
echo "<pre>";
foreach ($sessionInfo as $key => $value) {
    if (is_array($value)) {
        echo "$key: " . json_encode($value, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "$key: " . ($value ?: 'null') . "\n";
    }
}
echo "</pre>";

// Test session security settings
echo "<h2>Step 7: Security Settings</h2>";
$cookieParams = session_get_cookie_params();
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$securityChecks = [
    'session.cookie_httponly' => ['expected' => '1', 'actual' => ini_get('session.cookie_httponly')],
    'session.use_only_cookies' => ['expected' => '1', 'actual' => ini_get('session.use_only_cookies')],
    'session.cookie_secure' => ['expected' => '0', 'actual' => ini_get('session.cookie_secure')], // 0 for HTTP, 1 for HTTPS
    'session.name' => ['expected' => 'ARABIC_NEWS_SESSION', 'actual' => session_name()],
    'session.gc_maxlifetime' => ['expected' => '3600', 'actual' => ini_get('session.gc_maxlifetime')],
];

foreach ($securityChecks as $setting => $check) {
    $status = ($check['actual'] == $check['expected']) ? '✅ OK' : '❌ Issue';
    echo "<tr>";
    echo "<td>$setting</td>";
    echo "<td>{$check['actual']}</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";

// Test session operations
echo "<h2>Step 8: Session Operations Test</h2>";

// Set test data
SessionManager::set('test_key', 'test_value');
echo "<p>✅ Set test_key = 'test_value'</p>";

// Get test data
$testValue = SessionManager::get('test_key');
echo "<p>✅ Retrieved test_key = '$testValue'</p>";

// Check if key exists
$hasKey = SessionManager::has('test_key');
echo "<p>✅ Has test_key: " . ($hasKey ? 'Yes' : 'No') . "</p>";

// Remove test data
SessionManager::remove('test_key');
echo "<p>✅ Removed test_key</p>";

// Verify removal
$hasKeyAfterRemoval = SessionManager::has('test_key');
echo "<p>✅ Has test_key after removal: " . ($hasKeyAfterRemoval ? 'Yes' : 'No') . "</p>";

// Final status
echo "<h2>🎯 Final Status</h2>";

$allGood = true;
$issues = [];

// Check for session warnings (this would be in error log)
if (session_status() !== PHP_SESSION_ACTIVE) {
    $allGood = false;
    $issues[] = "Session not active";
}

// Check security settings
foreach ($securityChecks as $setting => $check) {
    if ($check['actual'] != $check['expected']) {
        $allGood = false;
        $issues[] = "Security setting $setting not configured correctly";
    }
}

if ($allGood) {
    echo "<div style='background: #d1fae5; color: #059669; padding: 20px; border-radius: 10px; border: 2px solid #059669;'>";
    echo "<h3>🎉 Session Configuration Fixed!</h3>";
    echo "<p>✅ No session warnings</p>";
    echo "<p>✅ Session properly configured</p>";
    echo "<p>✅ Security settings applied</p>";
    echo "<p>✅ SessionManager working correctly</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fee2e2; color: #dc2626; padding: 20px; border-radius: 10px; border: 2px solid #dc2626;'>";
    echo "<h3>❌ Issues Found</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h3>🔗 Navigation</h3>";
echo "<p><a href='index.php' style='background: #1e40af; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Home</a></p>";
echo "<p><a href='login.php' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 Login</a></p>";
echo "<p><a href='admin/' style='background: #7c3aed; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>⚙️ Admin</a></p>";

echo "<h3>📝 Notes</h3>";
echo "<ul>";
echo "<li>If you see this page without warnings, the session configuration is fixed</li>";
echo "<li>The SessionManager provides secure session handling</li>";
echo "<li>CSRF protection is enabled</li>";
echo "<li>Session security settings are properly configured</li>";
echo "<li>You can delete this test file after verification</li>";
echo "</ul>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2, h3 {
    color: #1e40af;
}
table {
    margin: 10px 0;
}
th, td {
    padding: 8px 12px;
    text-align: left;
}
th {
    background: #e5e7eb;
}
pre {
    background: #f3f4f6;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
