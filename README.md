# الموقع الإخباري العربي الشامل

موقع إخباري عربي متكامل مبني بـ PHP مع نظام إدارة محتوى شامل ودعم RSS وجدول مباريات كرة القدم.

## المميزات الرئيسية

### 🌟 الوظائف الأساسية
- **تكامل موجز RSS** - تجميع الأخبار تلقائياً من مصادر متعددة
- **نظام إدارة محتوى متكامل** - إنشاء وتحرير ونشر المقالات
- **مصادقة المستخدمين** - نظام تحكم في الوصول بناءً على الأدوار
- **خاصية البحث المتقدم** - البحث في جميع المقالات والمحتوى
- **نظام التعليقات** - تفاعل المستخدمين مع المقالات
- **إدارة الفئات والوسوم** - تنظيم المحتوى بشكل منطقي

### 🎛️ لوحة التحكم الإدارية
- **لوحة تحكم شاملة** - إدارة جميع جوانب الموقع
- **إدارة المقالات** - إنشاء، تحرير، حذف، نشر/إلغاء النشر
- **إدارة المستخدمين** - مستويات صلاحيات مختلفة (Admin, Editor, User)
- **إدارة مصادر RSS** - إضافة وإزالة وتكوين الخلاصات
- **إدارة الفئات والوسوم** - تنظيم المحتوى
- **إعدادات الموقع** - تكوين شامل للموقع
- **التحليلات والتقارير** - إحصائيات مفصلة

### 🎨 التصميم والواجهة
- **تصميم احترافي وعصري** - واجهة مستخدم جميلة ومتطورة
- **تصميم متجاوب بالكامل** - يعمل على جميع الأجهزة
- **دعم RTL كامل** - للمحتوى العربي
- **مبادئ UX/UI حديثة** - تجربة مستخدم ممتازة
- **نظام ألوان متناسق** - علامة تجارية موحدة
- **أداء محسّن** - أوقات تحميل سريعة

### ⚽ ميزات إضافية
- **شريط أخبار عاجل** - مع رسوم متحركة RTL
- **جدول مباريات كرة القدم** - عرض وإدارة المباريات
- **مشاركة وسائل التواصل** - Facebook, Twitter, WhatsApp, Telegram
- **نشرة إخبارية** - نظام اشتراك
- **تصميم متجاوب للموبايل** - تركيز على الأجهزة المحمولة

## متطلبات النظام

- **PHP 7.4+** مع المكتبات التالية:
  - PDO MySQL
  - GD Library
  - cURL
  - mbstring
- **MySQL 5.7+** أو **MariaDB 10.2+**
- **Apache** أو **Nginx**
- **mod_rewrite** (للروابط الودية)

## التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/arabic-news-website.git
cd arabic-news-website
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة باسم `arabic_news`
2. قم بتشغيل الموقع - ستتم إنشاء الجداول تلقائياً
3. أو قم بتشغيل ملف SQL المرفق

### 3. تكوين الإعدادات
1. افتح `config/database.php`
2. قم بتعديل إعدادات قاعدة البيانات:
```php
private $host = 'localhost';
private $db_name = 'arabic_news';
private $username = 'your_username';
private $password = 'your_password';
```

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 uploads/images/
```

### 5. إعداد Cron Job (اختياري)
لتحديث RSS تلقائياً كل 30 دقيقة:
```bash
*/30 * * * * /usr/bin/php /path/to/your/site/rss/fetch.php
```

## الاستخدام

### تسجيل الدخول الأول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **رابط لوحة التحكم:** `yoursite.com/admin/`

⚠️ **مهم:** قم بتغيير كلمة المرور فوراً بعد التثبيت!

### إضافة مصادر RSS
1. اذهب إلى لوحة التحكم > RSS > إدارة المصادر
2. أضف مصادر RSS الخاصة بك
3. حدد القسم المناسب لكل مصدر
4. فعّل التحديث التلقائي

### إدارة المحتوى
1. **المقالات:** إنشاء وتحرير المقالات من لوحة التحكم
2. **الأقسام:** تنظيم المحتوى في أقسام منطقية
3. **الوسوم:** إضافة كلمات مفتاحية للمقالات
4. **التعليقات:** مراجعة وإدارة تعليقات المستخدمين

### إضافة المباريات
1. اذهب إلى لوحة التحكم > المباريات
2. أضف مباريات جديدة مع التفاصيل
3. حدّث النتائج بعد انتهاء المباريات

## هيكل المشروع

```
arabic-news-website/
├── admin/                  # لوحة التحكم الإدارية
│   ├── assets/            # ملفات CSS/JS للإدارة
│   ├── includes/          # ملفات مشتركة للإدارة
│   └── index.php          # الصفحة الرئيسية للإدارة
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات التنسيق
│   ├── js/               # ملفات JavaScript
│   └── images/           # الصور الثابتة
├── config/               # ملفات التكوين
│   ├── config.php        # الإعدادات العامة
│   └── database.php      # إعدادات قاعدة البيانات
├── includes/             # الملفات المشتركة
│   ├── header.php        # رأس الصفحة
│   └── footer.php        # تذييل الصفحة
├── rss/                  # نظام RSS
│   └── fetch.php         # جلب وتحليل RSS
├── uploads/              # الملفات المرفوعة
│   └── images/           # الصور المرفوعة
├── index.php             # الصفحة الرئيسية
├── article.php           # صفحة المقال
├── category.php          # صفحة القسم
├── search.php            # صفحة البحث
├── matches.php           # صفحة المباريات
├── login.php             # صفحة تسجيل الدخول
└── README.md             # هذا الملف
```

## الأمان

### إجراءات الأمان المطبقة
- **حماية CSRF** - رموز أمان للنماذج
- **تشفير كلمات المرور** - باستخدام password_hash()
- **تنظيف المدخلات** - منع SQL Injection و XSS
- **التحكم في الوصول** - نظام صلاحيات متدرج
- **رفع الملفات الآمن** - فحص نوع وحجم الملفات

### توصيات إضافية
1. استخدم HTTPS في الإنتاج
2. قم بتحديث PHP وMySQL بانتظام
3. اعمل نسخ احتياطية دورية
4. راقب سجلات الأخطاء
5. استخدم كلمات مرور قوية

## التخصيص

### تغيير التصميم
- عدّل ملفات CSS في `assets/css/`
- استخدم متغيرات CSS المعرّفة في `:root`
- جميع الألوان والخطوط قابلة للتخصيص

### إضافة ميزات جديدة
- اتبع هيكل MVC المستخدم
- استخدم PDO للتعامل مع قاعدة البيانات
- اتبع معايير الأمان المطبقة

## الدعم والمساهمة

### الإبلاغ عن المشاكل
- استخدم نظام Issues في GitHub
- قدم وصفاً مفصلاً للمشكلة
- أرفق لقطات شاشة إن أمكن

### المساهمة
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب كود نظيف ومعلق
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

- **Bootstrap** - إطار عمل CSS
- **Font Awesome** - الأيقونات
- **Chart.js** - الرسوم البيانية
- **Google Fonts** - خط Cairo العربي

---

**تم تطويره بـ ❤️ للمجتمع العربي**

للدعم الفني أو الاستفسارات، يرجى التواصل عبر Issues أو البريد الإلكتروني.
