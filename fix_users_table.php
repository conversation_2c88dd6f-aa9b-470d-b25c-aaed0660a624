<?php
/**
 * Quick Fix for Missing Users Table
 * This script specifically addresses the "users table doesn't exist" error
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Quick Fix: Users Table</h1>";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=localhost;dbname=arabic_news;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to database 'arabic_news'</p>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ Users table already exists</p>";
        
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p>✅ Admin user already exists</p>";
            echo "<p>📧 Email: " . $admin['email'] . "</p>";
            echo "<p>👤 Role: " . $admin['role'] . "</p>";
        } else {
            echo "<p>⚠️ Admin user missing, creating...</p>";
            
            // Create admin user
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'admin']);
            
            echo "<p>✅ Admin user created successfully</p>";
        }
        
    } else {
        echo "<p>❌ Users table missing, creating...</p>";
        
        // Create users table
        $pdo->exec("CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'editor', 'user') DEFAULT 'user',
            remember_token VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        echo "<p>✅ Users table created successfully</p>";
        
        // Create admin user
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'admin']);
        
        echo "<p>✅ Admin user created successfully</p>";
    }
    
    // Test admin login
    echo "<h2>🧪 Testing Admin Login</h2>";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "<div style='background: #d1fae5; color: #059669; padding: 15px; border-radius: 8px; border: 2px solid #059669;'>";
        echo "<h3>🎉 Success!</h3>";
        echo "<p>✅ Users table exists</p>";
        echo "<p>✅ Admin user configured</p>";
        echo "<p>✅ Password verification working</p>";
        echo "</div>";
        
        echo "<h3>📝 Login Credentials:</h3>";
        echo "<ul>";
        echo "<li><strong>Username:</strong> admin</li>";
        echo "<li><strong>Password:</strong> admin123</li>";
        echo "<li><strong>Email:</strong> <EMAIL></li>";
        echo "</ul>";
        
        echo "<h3>🚀 Next Steps:</h3>";
        echo "<p>You can now continue with the installation or login to the admin panel.</p>";
        echo "<p><a href='setup.php' style='background: #1e40af; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Continue Setup</a></p>";
        echo "<p><a href='admin/' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Admin Panel</a></p>";
        
    } else {
        echo "<div style='background: #fee2e2; color: #dc2626; padding: 15px; border-radius: 8px; border: 2px solid #dc2626;'>";
        echo "<h3>❌ Login Test Failed</h3>";
        echo "<p>There's still an issue with the admin user setup.</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    if (strpos($e->getMessage(), "Unknown database") !== false) {
        echo "<div style='background: #fef3c7; color: #92400e; padding: 15px; border-radius: 8px; border: 2px solid #f59e0b;'>";
        echo "<h3>⚠️ Database Not Found</h3>";
        echo "<p>The 'arabic_news' database doesn't exist. Creating it now...</p>";
        echo "</div>";
        
        try {
            // Create database first
            $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $pdo->exec("CREATE DATABASE IF NOT EXISTS arabic_news CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<p>✅ Database 'arabic_news' created</p>";
            
            // Now run the full initialization
            echo "<p>🔄 Please run the <a href='init_database.php'>full database initialization</a> now.</p>";
            
        } catch (PDOException $e2) {
            echo "<div style='background: #fee2e2; color: #dc2626; padding: 15px; border-radius: 8px; border: 2px solid #dc2626;'>";
            echo "<h3>❌ Database Creation Failed</h3>";
            echo "<p>Error: " . $e2->getMessage() . "</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #fee2e2; color: #dc2626; padding: 15px; border-radius: 8px; border: 2px solid #dc2626;'>";
        echo "<h3>❌ Database Error</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
        
        echo "<h3>🔧 Troubleshooting:</h3>";
        echo "<ul>";
        echo "<li>Make sure MySQL is running in XAMPP</li>";
        echo "<li>Check database credentials</li>";
        echo "<li>Try running <a href='init_database.php'>full database initialization</a></li>";
        echo "</ul>";
    }
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2, h3 {
    color: #1e40af;
}
</style>
