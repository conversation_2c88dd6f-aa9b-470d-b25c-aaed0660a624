        </div>
    </main>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <!-- Footer Top -->
            <div class="footer-top">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="widget-title">عن الموقع</h5>
                            <p class="widget-text">
                                <?php echo getSetting('site_description', SITE_DESCRIPTION); ?>
                            </p>
                            <div class="social-links">
                                <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-telegram"></i></a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="widget-title">روابط سريعة</h5>
                            <ul class="footer-links">
                                <li><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                                <li><a href="<?php echo SITE_URL; ?>/about.php">من نحن</a></li>
                                <li><a href="<?php echo SITE_URL; ?>/contact.php">اتصل بنا</a></li>
                                <li><a href="<?php echo SITE_URL; ?>/privacy.php">سياسة الخصوصية</a></li>
                                <li><a href="<?php echo SITE_URL; ?>/terms.php">شروط الاستخدام</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="widget-title">الأقسام</h5>
                            <ul class="footer-links">
                                <?php
                                try {
                                    $stmt = $pdo->prepare("SELECT name, slug FROM categories ORDER BY name LIMIT 6");
                                    $stmt->execute();
                                    $categories = $stmt->fetchAll();
                                    
                                    foreach ($categories as $category) {
                                        echo '<li><a href="' . SITE_URL . '/category.php?slug=' . $category['slug'] . '">' . $category['name'] . '</a></li>';
                                    }
                                } catch(PDOException $e) {
                                    // Handle error silently
                                }
                                ?>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="widget-title">النشرة الإخبارية</h5>
                            <p class="widget-text">اشترك في نشرتنا الإخبارية لتصلك آخر الأخبار</p>
                            <form class="newsletter-form" id="newsletterForm">
                                <div class="input-group">
                                    <input type="email" class="form-control" placeholder="بريدك الإلكتروني" required>
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-menu text-end">
                            <a href="<?php echo SITE_URL; ?>/sitemap.php">خريطة الموقع</a>
                            <span class="separator">|</span>
                            <a href="<?php echo SITE_URL; ?>/rss.php">RSS</a>
                            <span class="separator">|</span>
                            <a href="<?php echo SITE_URL; ?>/contact.php">اتصل بنا</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="<?php echo SITE_URL; ?>/assets/js/main.js"></script>
    
    <!-- Real-time Clock -->
    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // Update time every minute
        setInterval(updateTime, 60000);
        updateTime(); // Initial call
    </script>

    <!-- Newsletter Form Handler -->
    <script>
        document.getElementById('newsletterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            
            // Here you would typically send the email to your backend
            // For now, we'll just show a success message
            alert('تم الاشتراك بنجاح! شكراً لك.');
            this.reset();
        });
    </script>

    <!-- Breaking News Ticker Animation -->
    <script>
        const tickerText = document.getElementById('breakingNewsText');
        if (tickerText) {
            // Add smooth scrolling animation to breaking news
            tickerText.style.animationDuration = '30s';
        }
    </script>

    <!-- Google Analytics (Replace with your tracking ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_TRACKING_ID');
    </script>

</body>
</html>
