<?php
require_once 'config/config.php';

$pageTitle = 'الصفحة غير موجودة - 404';
$pageDescription = 'الصفحة التي تبحث عنها غير موجودة';

include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="error-404">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <div class="error-content">
                <h1 class="error-title">404</h1>
                <h2 class="error-subtitle">الصفحة غير موجودة</h2>
                <p class="error-message">
                    عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
                </p>
                
                <div class="error-actions">
                    <a href="<?php echo SITE_URL; ?>/" class="btn btn-primary btn-lg">
                        <i class="fas fa-home"></i>
                        العودة إلى الرئيسية
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-right"></i>
                        العودة للخلف
                    </button>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="error-search">
            <h4>أو ابحث عما تريد:</h4>
            <form action="<?php echo SITE_URL; ?>/search.php" method="GET" class="search-form">
                <div class="input-group">
                    <input type="text" name="q" class="form-control search-input" 
                           placeholder="ابحث في الموقع..." required>
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- Popular Articles -->
        <div class="popular-articles-section">
            <h4>مقالات شائعة قد تهمك:</h4>
            <div class="row">
                <?php
                try {
                    $stmt = $pdo->prepare("
                        SELECT a.*, c.name as category_name, c.slug as category_slug
                        FROM articles a 
                        LEFT JOIN categories c ON a.category_id = c.id 
                        WHERE a.status = 'published'
                        ORDER BY a.views DESC 
                        LIMIT 6
                    ");
                    $stmt->execute();
                    $popularArticles = $stmt->fetchAll();
                    
                    foreach ($popularArticles as $article) {
                        echo '<div class="col-md-6 mb-3">
                                <div class="popular-article-card">
                                    <img src="' . ($article['featured_image'] ? SITE_URL . '/uploads/' . $article['featured_image'] : SITE_URL . '/assets/images/placeholder.jpg') . '" 
                                         alt="' . htmlspecialchars($article['title']) . '" 
                                         class="popular-article-image">
                                    <div class="popular-article-content">
                                        <div class="popular-article-category">
                                            <a href="' . SITE_URL . '/category.php?slug=' . $article['category_slug'] . '">' . $article['category_name'] . '</a>
                                        </div>
                                        <h6 class="popular-article-title">
                                            <a href="' . SITE_URL . '/article.php?slug=' . $article['slug'] . '">' . 
                                               truncateText($article['title'], 60) . '</a>
                                        </h6>
                                        <div class="popular-article-meta">
                                            <span><i class="fas fa-eye"></i> ' . formatNumber($article['views']) . '</span>
                                            <span><i class="fas fa-clock"></i> ' . timeAgo($article['created_at']) . '</span>
                                        </div>
                                    </div>
                                </div>
                              </div>';
                    }
                } catch(PDOException $e) {
                    echo '<p class="text-center">حدث خطأ في تحميل المقالات</p>';
                }
                ?>
            </div>
        </div>

        <!-- Categories -->
        <div class="categories-section">
            <h4>تصفح حسب القسم:</h4>
            <div class="categories-grid">
                <?php
                try {
                    $stmt = $pdo->prepare("
                        SELECT c.*, COUNT(a.id) as article_count 
                        FROM categories c 
                        LEFT JOIN articles a ON c.id = a.category_id AND a.status = 'published'
                        GROUP BY c.id 
                        ORDER BY c.name
                    ");
                    $stmt->execute();
                    $categories = $stmt->fetchAll();
                    
                    foreach ($categories as $category) {
                        echo '<a href="' . SITE_URL . '/category.php?slug=' . $category['slug'] . '" class="category-card">
                                <div class="category-name">' . $category['name'] . '</div>
                                <div class="category-count">' . $category['article_count'] . ' مقال</div>
                              </a>';
                    }
                } catch(PDOException $e) {
                    echo '<p class="text-center">حدث خطأ في تحميل الأقسام</p>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<style>
.error-404 {
    text-align: center;
    padding: 60px 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 40px;
}

.error-icon {
    font-size: 5rem;
    color: var(--secondary-color);
    margin-bottom: 30px;
}

.error-title {
    font-size: 6rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
    line-height: 1;
}

.error-subtitle {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 20px;
}

.error-message {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 40px;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.error-search {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 40px;
    text-align: center;
}

.error-search h4 {
    color: var(--text-color);
    margin-bottom: 20px;
}

.error-search .search-form {
    max-width: 500px;
    margin: 0 auto;
}

.popular-articles-section,
.categories-section {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 40px;
}

.popular-articles-section h4,
.categories-section h4 {
    color: var(--text-color);
    margin-bottom: 25px;
    text-align: center;
}

.popular-article-card {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    height: 100%;
}

.popular-article-card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.popular-article-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    flex-shrink: 0;
}

.popular-article-content {
    flex: 1;
}

.popular-article-category {
    margin-bottom: 8px;
}

.popular-article-category a {
    background: var(--primary-color);
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    text-decoration: none;
    font-size: 0.7rem;
    transition: var(--transition);
}

.popular-article-category a:hover {
    background: #1d4ed8;
}

.popular-article-title {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.popular-article-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.popular-article-title a:hover {
    color: var(--primary-color);
}

.popular-article-meta {
    font-size: 0.75rem;
    color: var(--text-light);
}

.popular-article-meta span {
    margin-left: 10px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.category-card {
    background: var(--light-color);
    padding: 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
}

.category-card:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

.category-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
    transition: var(--transition);
}

.category-count {
    font-size: 0.9rem;
    color: var(--text-light);
    transition: var(--transition);
}

.category-card:hover .category-name,
.category-card:hover .category-count {
    color: white;
}

@media (max-width: 768px) {
    .error-title {
        font-size: 4rem;
    }
    
    .error-subtitle {
        font-size: 1.5rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .popular-article-card {
        flex-direction: column;
        text-align: center;
    }
    
    .popular-article-image {
        width: 100%;
        height: 150px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 576px) {
    .error-404 {
        padding: 40px 20px;
    }
    
    .error-title {
        font-size: 3rem;
    }
    
    .error-subtitle {
        font-size: 1.3rem;
    }
    
    .popular-articles-section,
    .categories-section,
    .error-search {
        padding: 20px;
    }
}
</style>

<script>
// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Animate the 404 number
    const errorTitle = document.querySelector('.error-title');
    if (errorTitle) {
        errorTitle.style.opacity = '0';
        errorTitle.style.transform = 'scale(0.5)';
        
        setTimeout(() => {
            errorTitle.style.transition = 'all 0.5s ease';
            errorTitle.style.opacity = '1';
            errorTitle.style.transform = 'scale(1)';
        }, 300);
    }
    
    // Focus on search input
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        setTimeout(() => {
            searchInput.focus();
        }, 1000);
    }
});
</script>

<?php include 'includes/footer.php'; ?>
