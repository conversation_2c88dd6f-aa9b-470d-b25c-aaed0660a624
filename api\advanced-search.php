<?php
/**
 * Advanced Search API Endpoint
 * Handles advanced search requests with multiple filters
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Get search parameters
    $keywords = isset($_GET['keywords']) ? trim($_GET['keywords']) : '';
    $category = isset($_GET['category']) ? trim($_GET['category']) : '';
    $author = isset($_GET['author']) ? trim($_GET['author']) : '';
    $date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
    $date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(50, intval($_GET['limit']))) : 12;
    $offset = ($page - 1) * $limit;

    // Build the search query
    $whereConditions = ["a.status = 'published'"];
    $params = [];
    $paramTypes = '';

    // Keywords search (title and content)
    if (!empty($keywords)) {
        $whereConditions[] = "(a.title LIKE ? OR a.content LIKE ? OR a.excerpt LIKE ?)";
        $searchTerm = '%' . $keywords . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $paramTypes .= 'sss';
    }

    // Category filter
    if (!empty($category)) {
        $whereConditions[] = "c.slug = ?";
        $params[] = $category;
        $paramTypes .= 's';
    }

    // Author filter
    if (!empty($author)) {
        $whereConditions[] = "u.username LIKE ?";
        $params[] = '%' . $author . '%';
        $paramTypes .= 's';
    }

    // Date range filter
    if (!empty($date_from)) {
        $whereConditions[] = "DATE(a.created_at) >= ?";
        $params[] = $date_from;
        $paramTypes .= 's';
    }

    if (!empty($date_to)) {
        $whereConditions[] = "DATE(a.created_at) <= ?";
        $params[] = $date_to;
        $paramTypes .= 's';
    }

    // Build the main query
    $whereClause = implode(' AND ', $whereConditions);
    
    $query = "
        SELECT 
            a.id,
            a.title,
            a.slug,
            a.excerpt,
            a.content,
            a.featured_image,
            a.views,
            a.created_at,
            a.updated_at,
            c.name as category_name,
            c.slug as category_slug,
            u.username as author_name,
            u.display_name as author_display_name
        FROM articles a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.author_id = u.id
        WHERE {$whereClause}
        ORDER BY a.created_at DESC
        LIMIT ? OFFSET ?
    ";

    // Add limit and offset parameters
    $params[] = $limit;
    $params[] = $offset;
    $paramTypes .= 'ii';

    // Prepare and execute the query
    $stmt = $pdo->prepare($query);
    
    if (!empty($params)) {
        $stmt->execute($params);
    } else {
        $stmt->execute();
    }
    
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get total count for pagination
    $countQuery = "
        SELECT COUNT(*) as total
        FROM articles a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.author_id = u.id
        WHERE {$whereClause}
    ";

    $countStmt = $pdo->prepare($countQuery);
    
    if (!empty($params)) {
        // Remove limit and offset parameters for count query
        $countParams = array_slice($params, 0, -2);
        $countStmt->execute($countParams);
    } else {
        $countStmt->execute();
    }
    
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    $totalPages = ceil($totalCount / $limit);

    // Process articles data
    $processedArticles = [];
    foreach ($articles as $article) {
        $processedArticle = [
            'id' => $article['id'],
            'title' => htmlspecialchars($article['title']),
            'slug' => $article['slug'],
            'excerpt' => $article['excerpt'] ? htmlspecialchars($article['excerpt']) : truncateText(strip_tags($article['content']), 150),
            'featured_image' => $article['featured_image'] ? SITE_URL . '/uploads/' . $article['featured_image'] : SITE_URL . '/assets/images/placeholder.jpg',
            'views' => formatNumber($article['views']),
            'created_at' => $article['created_at'],
            'time_ago' => timeAgo($article['created_at']),
            'category' => [
                'name' => $article['category_name'],
                'slug' => $article['category_slug']
            ],
            'author' => [
                'username' => $article['author_name'],
                'display_name' => $article['author_display_name'] ?: $article['author_name']
            ],
            'url' => SITE_URL . '/article.php?slug=' . $article['slug']
        ];
        
        $processedArticles[] = $processedArticle;
    }

    // Prepare response
    $response = [
        'success' => true,
        'data' => [
            'articles' => $processedArticles,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_count' => $totalCount,
                'per_page' => $limit,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ],
            'search_params' => [
                'keywords' => $keywords,
                'category' => $category,
                'author' => $author,
                'date_from' => $date_from,
                'date_to' => $date_to
            ]
        ],
        'message' => $totalCount > 0 ? "تم العثور على {$totalCount} نتيجة" : 'لم يتم العثور على نتائج'
    ];

    // Log search for analytics (optional)
    if (!empty($keywords) || !empty($category) || !empty($author)) {
        try {
            $logQuery = "INSERT INTO search_logs (keywords, category, author, date_from, date_to, results_count, ip_address, user_agent, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            $logStmt = $pdo->prepare($logQuery);
            $logStmt->execute([
                $keywords,
                $category,
                $author,
                $date_from ?: null,
                $date_to ?: null,
                $totalCount,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            // Log error but don't fail the search
            error_log("Search logging failed: " . $e->getMessage());
        }
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ في البحث',
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Helper function to format numbers
 */
function formatNumber($num) {
    if ($num >= 1000000) {
        return round($num / 1000000, 1) . 'M';
    } elseif ($num >= 1000) {
        return round($num / 1000, 1) . 'K';
    }
    return number_format($num);
}

/**
 * Helper function to calculate time ago
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'منذ لحظات';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    if ($time < 31536000) return 'منذ ' . floor($time/2592000) . ' شهر';
    
    return 'منذ ' . floor($time/31536000) . ' سنة';
}

/**
 * Helper function to truncate text
 */
function truncateText($text, $length = 100) {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . '...';
}
?>
