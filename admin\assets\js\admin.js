// Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Sidebar toggle functionality
    initSidebarToggle();
    
    // Form enhancements
    initFormEnhancements();
    
    // Data tables
    initDataTables();
    
    // File upload previews
    initFileUploads();
    
    // Bulk actions
    initBulkActions();
    
    // Auto-save functionality
    initAutoSave();
    
    // Real-time updates
    initRealTimeUpdates();
    
    // Charts and analytics
    initCharts();
    
});

// Sidebar Toggle
function initSidebarToggle() {
    const sidebar = document.querySelector('.admin-sidebar');
    const main = document.querySelector('.admin-main');
    const toggleBtn = document.getElementById('sidebarToggle');
    
    if (toggleBtn) {
        toggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            main.classList.toggle('expanded');
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });
    }
    
    // Restore sidebar state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed');
    if (sidebarCollapsed === 'true') {
        sidebar.classList.add('collapsed');
        main.classList.add('expanded');
    }
    
    // Auto-collapse on mobile
    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        main.classList.add('expanded');
    }
}

// Form Enhancements
function initFormEnhancements() {
    // Character counters
    const textareas = document.querySelectorAll('textarea[data-max-length]');
    textareas.forEach(textarea => {
        const maxLength = parseInt(textarea.dataset.maxLength);
        const counter = document.createElement('div');
        counter.className = 'character-counter';
        textarea.parentNode.appendChild(counter);
        
        function updateCounter() {
            const remaining = maxLength - textarea.value.length;
            counter.textContent = `${remaining} حرف متبقي`;
            counter.className = 'character-counter ' + (remaining < 50 ? 'warning' : '');
        }
        
        textarea.addEventListener('input', updateCounter);
        updateCounter();
    });
    
    // Slug generation
    const titleInputs = document.querySelectorAll('input[name="title"]');
    titleInputs.forEach(titleInput => {
        const slugInput = document.querySelector('input[name="slug"]');
        if (slugInput) {
            titleInput.addEventListener('input', function() {
                if (!slugInput.dataset.manual) {
                    slugInput.value = generateSlug(this.value);
                }
            });
            
            slugInput.addEventListener('input', function() {
                this.dataset.manual = 'true';
            });
        }
    });
    
    // Rich text editor initialization
    initRichTextEditor();
}

// Generate slug from text
function generateSlug(text) {
    return text
        .toLowerCase()
        .trim()
        .replace(/[^\u0600-\u06FF\w\s-]/g, '') // Keep Arabic, Latin, numbers, spaces, hyphens
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
}

// Rich Text Editor
function initRichTextEditor() {
    const editors = document.querySelectorAll('.rich-editor');
    editors.forEach(editor => {
        // Create toolbar
        const toolbar = createEditorToolbar();
        editor.parentNode.insertBefore(toolbar, editor);
        
        // Add event listeners
        toolbar.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                e.preventDefault();
                const command = e.target.dataset.command;
                const value = e.target.dataset.value;
                executeEditorCommand(editor, command, value);
            }
        });
    });
}

function createEditorToolbar() {
    const toolbar = document.createElement('div');
    toolbar.className = 'editor-toolbar';
    toolbar.innerHTML = `
        <button type="button" data-command="bold" title="عريض">
            <i class="fas fa-bold"></i>
        </button>
        <button type="button" data-command="italic" title="مائل">
            <i class="fas fa-italic"></i>
        </button>
        <button type="button" data-command="underline" title="تحته خط">
            <i class="fas fa-underline"></i>
        </button>
        <div class="toolbar-separator"></div>
        <button type="button" data-command="insertOrderedList" title="قائمة مرقمة">
            <i class="fas fa-list-ol"></i>
        </button>
        <button type="button" data-command="insertUnorderedList" title="قائمة نقطية">
            <i class="fas fa-list-ul"></i>
        </button>
        <div class="toolbar-separator"></div>
        <button type="button" data-command="createLink" title="رابط">
            <i class="fas fa-link"></i>
        </button>
        <button type="button" data-command="insertImage" title="صورة">
            <i class="fas fa-image"></i>
        </button>
    `;
    return toolbar;
}

function executeEditorCommand(editor, command, value) {
    editor.focus();
    
    if (command === 'createLink') {
        const url = prompt('أدخل الرابط:');
        if (url) {
            document.execCommand(command, false, url);
        }
    } else if (command === 'insertImage') {
        const url = prompt('أدخل رابط الصورة:');
        if (url) {
            document.execCommand(command, false, url);
        }
    } else {
        document.execCommand(command, false, value);
    }
}

// Data Tables
function initDataTables() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(table => {
        // Add sorting functionality
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this.dataset.sort);
            });
        });
        
        // Add search functionality
        const searchInput = table.parentNode.querySelector('.table-search');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                filterTable(table, this.value);
            });
        }
    });
}

function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => th.dataset.sort === column);
    
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        if (!isNaN(aValue) && !isNaN(bValue)) {
            return parseFloat(aValue) - parseFloat(bValue);
        }
        
        return aValue.localeCompare(bValue, 'ar');
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(term) ? '' : 'none';
    });
}

// File Upload Previews
function initFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            handleFilePreview(this);
        });
    });
}

function handleFilePreview(input) {
    const file = input.files[0];
    if (!file) return;
    
    const previewContainer = input.parentNode.querySelector('.file-preview') || 
                           createPreviewContainer(input);
    
    if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewContainer.innerHTML = `
                <img src="${e.target.result}" alt="Preview" class="preview-image">
                <button type="button" class="btn btn-sm btn-danger remove-preview">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            previewContainer.querySelector('.remove-preview').addEventListener('click', function() {
                input.value = '';
                previewContainer.innerHTML = '';
            });
        };
        reader.readAsDataURL(file);
    } else {
        previewContainer.innerHTML = `
            <div class="file-info">
                <i class="fas fa-file"></i>
                <span>${file.name}</span>
                <button type="button" class="btn btn-sm btn-danger remove-preview">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        previewContainer.querySelector('.remove-preview').addEventListener('click', function() {
            input.value = '';
            previewContainer.innerHTML = '';
        });
    }
}

function createPreviewContainer(input) {
    const container = document.createElement('div');
    container.className = 'file-preview';
    input.parentNode.appendChild(container);
    return container;
}

// Bulk Actions
function initBulkActions() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('input[name="selected_items[]"]');
    const bulkActionForm = document.getElementById('bulkActionForm');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionButtons();
        });
    }
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButtons);
    });
    
    if (bulkActionForm) {
        bulkActionForm.addEventListener('submit', function(e) {
            const selectedItems = document.querySelectorAll('input[name="selected_items[]"]:checked');
            if (selectedItems.length === 0) {
                e.preventDefault();
                alert('يرجى اختيار عنصر واحد على الأقل');
            }
        });
    }
}

function updateBulkActionButtons() {
    const selectedItems = document.querySelectorAll('input[name="selected_items[]"]:checked');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (bulkActions) {
        bulkActions.style.display = selectedItems.length > 0 ? 'block' : 'none';
    }
}

// Auto-save functionality
function initAutoSave() {
    const forms = document.querySelectorAll('form[data-autosave]');
    forms.forEach(form => {
        const formId = form.id || 'form_' + Date.now();
        form.id = formId;
        
        const inputs = form.querySelectorAll('input, textarea, select');
        
        // Load saved data
        inputs.forEach(input => {
            const savedValue = localStorage.getItem(`autosave_${formId}_${input.name}`);
            if (savedValue && !input.value) {
                input.value = savedValue;
            }
        });
        
        // Save data on input
        inputs.forEach(input => {
            input.addEventListener('input', debounce(function() {
                localStorage.setItem(`autosave_${formId}_${input.name}`, input.value);
                showAutoSaveIndicator();
            }, 1000));
        });
        
        // Clear saved data on successful submit
        form.addEventListener('submit', function() {
            inputs.forEach(input => {
                localStorage.removeItem(`autosave_${formId}_${input.name}`);
            });
        });
    });
}

function showAutoSaveIndicator() {
    const indicator = document.getElementById('autosaveIndicator') || createAutoSaveIndicator();
    indicator.style.display = 'block';
    indicator.textContent = 'تم الحفظ التلقائي';
    
    setTimeout(() => {
        indicator.style.display = 'none';
    }, 2000);
}

function createAutoSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'autosaveIndicator';
    indicator.className = 'autosave-indicator';
    indicator.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: var(--admin-success);
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        z-index: 1000;
        display: none;
    `;
    document.body.appendChild(indicator);
    return indicator;
}

// Real-time updates
function initRealTimeUpdates() {
    // Check for new notifications every 5 minutes
    setInterval(checkNotifications, 5 * 60 * 1000);
    
    // Update timestamps every minute
    setInterval(updateTimestamps, 60 * 1000);
}

function checkNotifications() {
    fetch('/admin/api/notifications.php')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadges(data);
        })
        .catch(error => {
            console.log('Notification check failed:', error);
        });
}

function updateNotificationBadges(data) {
    if (data.comments) {
        updateBadge('comments', data.comments);
    }
    if (data.articles) {
        updateBadge('articles', data.articles);
    }
}

function updateBadge(type, count) {
    const badge = document.querySelector(`[href*="${type}"] .badge`);
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

function updateTimestamps() {
    const timestamps = document.querySelectorAll('[data-timestamp]');
    timestamps.forEach(element => {
        const timestamp = parseInt(element.dataset.timestamp);
        element.textContent = timeAgo(timestamp);
    });
}

// Charts and Analytics
function initCharts() {
    // Views chart
    const viewsChart = document.getElementById('viewsChart');
    if (viewsChart) {
        createViewsChart(viewsChart);
    }
    
    // Categories chart
    const categoriesChart = document.getElementById('categoriesChart');
    if (categoriesChart) {
        createCategoriesChart(categoriesChart);
    }
}

function createViewsChart(canvas) {
    const ctx = canvas.getContext('2d');
    
    // Sample data - replace with actual data from server
    const data = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'المشاهدات',
            data: [12000, 19000, 15000, 25000, 22000, 30000],
            borderColor: 'rgb(30, 64, 175)',
            backgroundColor: 'rgba(30, 64, 175, 0.1)',
            tension: 0.4
        }]
    };
    
    new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function createCategoriesChart(canvas) {
    const ctx = canvas.getContext('2d');
    
    // Sample data - replace with actual data from server
    const data = {
        labels: ['رياضة', 'تكنولوجيا', 'اقتصاد', 'سياسة', 'صحة'],
        datasets: [{
            data: [30, 25, 20, 15, 10],
            backgroundColor: [
                '#1e40af',
                '#059669',
                '#f59e0b',
                '#dc2626',
                '#7c3aed'
            ]
        }]
    };
    
    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function timeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'منذ لحظات';
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    if (days < 30) return `منذ ${days} يوم`;
    
    return new Date(timestamp).toLocaleDateString('ar-SA');
}

// Toast notifications
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type} show`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

function getToastIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'times-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

// Export functions for global use
window.adminJS = {
    showToast,
    generateSlug,
    debounce,
    timeAgo
};
