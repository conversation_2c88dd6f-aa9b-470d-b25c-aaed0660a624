        </div>
    </main>

    <!-- Admin Footer -->
    <footer class="admin-footer">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="footer-text">
                        &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6">
                    <div class="footer-links text-end">
                        <a href="<?php echo SITE_URL; ?>/" target="_blank">عرض الموقع</a>
                        <span class="separator">|</span>
                        <a href="<?php echo SITE_URL; ?>/admin/help.php">المساعدة</a>
                        <span class="separator">|</span>
                        <a href="<?php echo SITE_URL; ?>/admin/support.php">الدعم الفني</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Admin JavaScript -->
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/admin.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="<?php echo SITE_URL; ?>/assets/js/main.js"></script>

    <!-- Auto-save functionality for forms -->
    <script>
        // Auto-save form data to localStorage
        function autoSaveForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            const inputs = form.querySelectorAll('input, textarea, select');
            
            // Load saved data
            inputs.forEach(input => {
                const savedValue = localStorage.getItem(`autosave_${formId}_${input.name}`);
                if (savedValue && !input.value) {
                    input.value = savedValue;
                }
            });
            
            // Save data on input
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    localStorage.setItem(`autosave_${formId}_${this.name}`, this.value);
                });
            });
            
            // Clear saved data on successful submit
            form.addEventListener('submit', function() {
                inputs.forEach(input => {
                    localStorage.removeItem(`autosave_${formId}_${input.name}`);
                });
            });
        }
        
        // Initialize auto-save for article forms
        document.addEventListener('DOMContentLoaded', function() {
            autoSaveForm('articleForm');
            autoSaveForm('categoryForm');
            autoSaveForm('userForm');
        });
    </script>

    <!-- Confirmation dialogs -->
    <script>
        // Confirm delete actions
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('delete-btn') || e.target.closest('.delete-btn')) {
                if (!confirm('هل أنت متأكد من الحذف؟ لا يمكن التراجع عن هذا الإجراء.')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
        
        // Confirm bulk actions
        function confirmBulkAction(action) {
            const checkedItems = document.querySelectorAll('input[name="selected_items[]"]:checked');
            if (checkedItems.length === 0) {
                alert('يرجى اختيار عنصر واحد على الأقل');
                return false;
            }
            
            let message = '';
            switch(action) {
                case 'delete':
                    message = `هل أنت متأكد من حذف ${checkedItems.length} عنصر؟`;
                    break;
                case 'publish':
                    message = `هل أنت متأكد من نشر ${checkedItems.length} عنصر؟`;
                    break;
                case 'unpublish':
                    message = `هل أنت متأكد من إلغاء نشر ${checkedItems.length} عنصر؟`;
                    break;
                default:
                    message = `هل أنت متأكد من تنفيذ هذا الإجراء على ${checkedItems.length} عنصر؟`;
            }
            
            return confirm(message);
        }
    </script>

    <!-- Real-time notifications -->
    <script>
        // Check for new comments every 5 minutes
        function checkNewComments() {
            fetch('<?php echo SITE_URL; ?>/admin/api/check-notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.newComments > 0) {
                        updateNotificationBadge('comments', data.newComments);
                    }
                })
                .catch(error => {
                    console.log('Notification check failed:', error);
                });
        }
        
        function updateNotificationBadge(type, count) {
            const badge = document.querySelector(`[href*="${type}"] .badge`);
            if (badge) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline' : 'none';
            }
        }
        
        // Check notifications every 5 minutes
        setInterval(checkNewComments, 5 * 60 * 1000);
    </script>

    <!-- Sidebar toggle for mobile -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.querySelector('.admin-sidebar');
            const main = document.querySelector('.admin-main');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    main.classList.toggle('expanded');
                });
            }
            
            // Auto-collapse sidebar on mobile
            if (window.innerWidth <= 768) {
                sidebar.classList.add('collapsed');
                main.classList.add('expanded');
            }
        });
    </script>

    <!-- Form validation enhancement -->
    <script>
        // Enhanced form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form[data-validate]');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    let isValid = true;
                    
                    // Check required fields
                    const requiredFields = form.querySelectorAll('[required]');
                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.classList.add('is-invalid');
                            isValid = false;
                        } else {
                            field.classList.remove('is-invalid');
                        }
                    });
                    
                    // Check email fields
                    const emailFields = form.querySelectorAll('input[type="email"]');
                    emailFields.forEach(field => {
                        if (field.value && !isValidEmail(field.value)) {
                            field.classList.add('is-invalid');
                            isValid = false;
                        }
                    });
                    
                    // Check URL fields
                    const urlFields = form.querySelectorAll('input[type="url"]');
                    urlFields.forEach(field => {
                        if (field.value && !isValidURL(field.value)) {
                            field.classList.add('is-invalid');
                            isValid = false;
                        }
                    });
                    
                    if (!isValid) {
                        e.preventDefault();
                        const firstInvalid = form.querySelector('.is-invalid');
                        if (firstInvalid) {
                            firstInvalid.focus();
                            firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                });
            });
        });
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function isValidURL(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        }
    </script>

    <!-- Image preview functionality -->
    <script>
        function previewImage(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }
    </script>

    <!-- Rich text editor initialization -->
    <script>
        // Initialize rich text editor for content areas
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('.rich-editor');
            textareas.forEach(textarea => {
                // You can integrate a rich text editor like TinyMCE or CKEditor here
                // For now, we'll add basic formatting buttons
                addFormattingButtons(textarea);
            });
        });
        
        function addFormattingButtons(textarea) {
            const toolbar = document.createElement('div');
            toolbar.className = 'editor-toolbar';
            toolbar.innerHTML = `
                <button type="button" onclick="insertText('${textarea.id}', '**', '**')" title="عريض">
                    <i class="fas fa-bold"></i>
                </button>
                <button type="button" onclick="insertText('${textarea.id}', '*', '*')" title="مائل">
                    <i class="fas fa-italic"></i>
                </button>
                <button type="button" onclick="insertText('${textarea.id}', '[', '](url)')" title="رابط">
                    <i class="fas fa-link"></i>
                </button>
            `;
            
            textarea.parentNode.insertBefore(toolbar, textarea);
        }
        
        function insertText(textareaId, before, after) {
            const textarea = document.getElementById(textareaId);
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const selectedText = textarea.value.substring(start, end);
            const newText = before + selectedText + after;
            
            textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length);
        }
    </script>

</body>
</html>
