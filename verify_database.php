<?php
/**
 * Database Verification Script
 * This script verifies that all tables and data are properly created
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Database Verification</h1>";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=localhost;dbname=arabic_news;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>✅ Database Connection Successful</h2>";
    
    // Check all tables
    echo "<h2>📋 Table Structure Verification</h2>";
    
    $expectedTables = [
        'users' => ['id', 'username', 'email', 'password', 'role', 'remember_token', 'created_at', 'updated_at'],
        'categories' => ['id', 'name', 'slug', 'description', 'created_at'],
        'tags' => ['id', 'name', 'slug', 'created_at'],
        'articles' => ['id', 'title', 'slug', 'content', 'excerpt', 'featured_image', 'category_id', 'author_id', 'status', 'is_breaking', 'views', 'created_at', 'updated_at', 'published_at'],
        'article_tags' => ['article_id', 'tag_id'],
        'comments' => ['id', 'article_id', 'user_id', 'author_name', 'author_email', 'content', 'status', 'created_at'],
        'rss_feeds' => ['id', 'name', 'url', 'category_id', 'is_active', 'last_fetched', 'created_at'],
        'matches' => ['id', 'home_team', 'away_team', 'home_score', 'away_score', 'match_date', 'league', 'status', 'created_at'],
        'settings' => ['id', 'setting_key', 'setting_value', 'created_at', 'updated_at']
    ];
    
    foreach ($expectedTables as $tableName => $expectedColumns) {
        echo "<h3>Table: $tableName</h3>";
        
        // Check if table exists
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        
        if ($stmt->rowCount() > 0) {
            echo "✅ Table exists<br>";
            
            // Check columns
            $stmt = $pdo->prepare("DESCRIBE $tableName");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "Columns: ";
            foreach ($expectedColumns as $expectedColumn) {
                if (in_array($expectedColumn, $columns)) {
                    echo "<span style='color: green;'>✅ $expectedColumn</span> ";
                } else {
                    echo "<span style='color: red;'>❌ $expectedColumn</span> ";
                }
            }
            echo "<br>";
            
            // Count records
            $stmt = $pdo->query("SELECT COUNT(*) FROM $tableName");
            $count = $stmt->fetchColumn();
            echo "Records: $count<br>";
            
        } else {
            echo "❌ Table missing<br>";
        }
        echo "<br>";
    }
    
    // Test admin user login
    echo "<h2>👤 Admin User Verification</h2>";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user found<br>";
        echo "📧 Email: " . $admin['email'] . "<br>";
        echo "👤 Role: " . $admin['role'] . "<br>";
        echo "📅 Created: " . $admin['created_at'] . "<br>";
        
        // Test password
        if (password_verify('admin123', $admin['password'])) {
            echo "✅ Password verification: SUCCESS<br>";
        } else {
            echo "❌ Password verification: FAILED<br>";
        }
    } else {
        echo "❌ Admin user not found<br>";
    }
    
    // Test categories
    echo "<h2>📂 Categories Verification</h2>";
    $stmt = $pdo->query("SELECT * FROM categories");
    $categories = $stmt->fetchAll();
    
    if (count($categories) > 0) {
        echo "✅ Categories found: " . count($categories) . "<br>";
        foreach ($categories as $category) {
            echo "- " . $category['name'] . " (" . $category['slug'] . ")<br>";
        }
    } else {
        echo "❌ No categories found<br>";
    }
    
    // Test settings
    echo "<h2>⚙️ Settings Verification</h2>";
    $stmt = $pdo->query("SELECT * FROM settings");
    $settings = $stmt->fetchAll();
    
    if (count($settings) > 0) {
        echo "✅ Settings found: " . count($settings) . "<br>";
        foreach ($settings as $setting) {
            echo "- " . $setting['setting_key'] . ": " . $setting['setting_value'] . "<br>";
        }
    } else {
        echo "❌ No settings found<br>";
    }
    
    // Test foreign key relationships
    echo "<h2>🔗 Foreign Key Relationships</h2>";
    
    // Test if we can insert an article
    try {
        $stmt = $pdo->prepare("INSERT INTO articles (title, slug, content, category_id, author_id, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['Test Article', 'test-article-' . time(), 'This is a test article content.', 1, 1, 'draft']);
        echo "✅ Article insertion test: SUCCESS<br>";
        
        // Clean up test article
        $pdo->exec("DELETE FROM articles WHERE slug LIKE 'test-article-%'");
        
    } catch (PDOException $e) {
        echo "❌ Article insertion test: FAILED - " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>🎯 Final Status</h2>";
    
    // Check if all essential components are working
    $allGood = true;
    $issues = [];
    
    // Check admin user
    if (!$admin || !password_verify('admin123', $admin['password'])) {
        $allGood = false;
        $issues[] = "Admin user login issue";
    }
    
    // Check essential tables
    $essentialTables = ['users', 'categories', 'articles', 'settings'];
    foreach ($essentialTables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() == 0) {
            $allGood = false;
            $issues[] = "Missing table: $table";
        }
    }
    
    if ($allGood) {
        echo "<div style='background: #d1fae5; color: #059669; padding: 20px; border-radius: 10px; border: 2px solid #059669;'>";
        echo "<h3>🎉 Database Setup Complete!</h3>";
        echo "<p>✅ All tables created successfully</p>";
        echo "<p>✅ Admin user configured</p>";
        echo "<p>✅ Default data inserted</p>";
        echo "<p>✅ Foreign key relationships working</p>";
        echo "</div>";
        
        echo "<h3>🚀 Ready to Use!</h3>";
        echo "<p><a href='index.php' style='background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 5px;'>🏠 Visit Website</a></p>";
        echo "<p><a href='admin/' style='background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 5px;'>⚙️ Admin Panel</a></p>";
        
        echo "<h3>📝 Login Credentials:</h3>";
        echo "<ul>";
        echo "<li><strong>Username:</strong> admin</li>";
        echo "<li><strong>Password:</strong> admin123</li>";
        echo "<li><strong>Email:</strong> <EMAIL></li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #fee2e2; color: #dc2626; padding: 20px; border-radius: 10px; border: 2px solid #dc2626;'>";
        echo "<h3>❌ Database Setup Issues Found</h3>";
        echo "<p>The following issues need to be resolved:</p>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<h3>🔧 Recommended Actions:</h3>";
        echo "<ol>";
        echo "<li><a href='init_database.php' style='color: #1e40af;'>Run Database Initialization</a></li>";
        echo "<li>Check MySQL server status</li>";
        echo "<li>Verify database credentials</li>";
        echo "<li>Check error logs</li>";
        echo "</ol>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #fee2e2; color: #dc2626; padding: 20px; border-radius: 10px; border: 2px solid #dc2626;'>";
    echo "<h2>❌ Database Connection Failed</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    
    echo "<h3>🔧 Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li>Make sure MySQL is running in XAMPP Control Panel</li>";
    echo "<li>Check if database 'arabic_news' exists</li>";
    echo "<li>Verify database credentials in config/database.php</li>";
    echo "<li>Try running <a href='init_database.php'>Database Initialization</a></li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2, h3 {
    color: #1e40af;
}
h3 {
    margin-top: 20px;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 5px;
}
</style>
