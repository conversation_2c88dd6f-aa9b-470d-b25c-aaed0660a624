# Minimal .htaccess for Arabic News Website
# Use this if the main .htaccess causes issues

# Enable error display (remove in production)
php_flag display_errors on
php_value error_reporting "E_ALL"

# Set charset
AddDefaultCharset UTF-8

# Basic security
<Files "*.php~">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files ".ht*">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent access to config files
<Files "config.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "database.php">
    Order allow,deny
    <PERSON>y from all
</Files>

# Error pages
ErrorDocument 404 /404.php

# Basic URL rewriting (optional)
# RewriteEngine On
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^article/([^/]+)/?$ article.php?slug=$1 [L,QSA]
