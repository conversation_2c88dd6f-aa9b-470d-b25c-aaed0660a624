<?php
/**
 * Session Management Helper
 * Handles secure session configuration and management
 */

class SessionManager {
    
    /**
     * Initialize secure session configuration
     */
    public static function init() {
        // Only configure if session hasn't started yet
        if (session_status() === PHP_SESSION_NONE) {
            
            // Security settings
            ini_set('session.cookie_httponly', 1);      // Prevent XSS attacks
            ini_set('session.use_only_cookies', 1);     // Only use cookies for session ID
            ini_set('session.cookie_secure', 0);        // Set to 1 for HTTPS in production
            ini_set('session.cookie_lifetime', 0);      // Session expires when browser closes
            ini_set('session.gc_maxlifetime', 3600);    // 1 hour garbage collection
            ini_set('session.name', 'ARABIC_NEWS_SESSION'); // Custom session name
            
            // Additional security settings
            ini_set('session.cookie_samesite', 'Lax');  // CSRF protection
            ini_set('session.use_strict_mode', 1);      // Reject uninitialized session IDs
            ini_set('session.sid_length', 48);          // Longer session ID
            ini_set('session.sid_bits_per_character', 6); // More entropy
            
            // Start the session
            session_start();
            
            // Regenerate session ID periodically for security
            self::regenerateIdIfNeeded();
        }
    }
    
    /**
     * Regenerate session ID if needed (every 30 minutes)
     */
    private static function regenerateIdIfNeeded() {
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 1800) { // 30 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
    
    /**
     * Check if session is active
     */
    public static function isActive() {
        return session_status() === PHP_SESSION_ACTIVE;
    }
    
    /**
     * Destroy session securely
     */
    public static function destroy() {
        if (self::isActive()) {
            // Clear session data
            $_SESSION = array();
            
            // Delete session cookie
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            
            // Destroy session
            session_destroy();
        }
    }
    
    /**
     * Set session variable securely
     */
    public static function set($key, $value) {
        if (!self::isActive()) {
            self::init();
        }
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get session variable
     */
    public static function get($key, $default = null) {
        if (!self::isActive()) {
            return $default;
        }
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }
    
    /**
     * Check if session variable exists
     */
    public static function has($key) {
        return self::isActive() && isset($_SESSION[$key]);
    }
    
    /**
     * Remove session variable
     */
    public static function remove($key) {
        if (self::isActive() && isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }
    
    /**
     * Flash message functionality
     */
    public static function flash($key, $value = null) {
        if ($value === null) {
            // Get flash message
            $message = self::get("flash_$key");
            self::remove("flash_$key");
            return $message;
        } else {
            // Set flash message
            self::set("flash_$key", $value);
        }
    }
    
    /**
     * CSRF Token management
     */
    public static function generateCSRFToken() {
        if (!self::has('csrf_token')) {
            self::set('csrf_token', bin2hex(random_bytes(32)));
        }
        return self::get('csrf_token');
    }
    
    /**
     * Verify CSRF Token
     */
    public static function verifyCSRFToken($token) {
        return self::has('csrf_token') && hash_equals(self::get('csrf_token'), $token);
    }
    
    /**
     * User authentication helpers
     */
    public static function login($userId, $username, $role) {
        // Regenerate session ID on login for security
        if (self::isActive()) {
            session_regenerate_id(true);
        }
        
        self::set('user_id', $userId);
        self::set('username', $username);
        self::set('user_role', $role);
        self::set('login_time', time());
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        self::destroy();
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        return self::has('user_id');
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin() {
        return self::get('user_role') === 'admin';
    }
    
    /**
     * Check if user is editor or admin
     */
    public static function isEditor() {
        $role = self::get('user_role');
        return $role === 'admin' || $role === 'editor';
    }
    
    /**
     * Get current user ID
     */
    public static function getUserId() {
        return self::get('user_id');
    }
    
    /**
     * Get current username
     */
    public static function getUsername() {
        return self::get('username');
    }
    
    /**
     * Get current user role
     */
    public static function getUserRole() {
        return self::get('user_role');
    }
    
    /**
     * Check session timeout
     */
    public static function checkTimeout($timeoutMinutes = 60) {
        $loginTime = self::get('login_time');
        if ($loginTime && (time() - $loginTime) > ($timeoutMinutes * 60)) {
            self::logout();
            return false;
        }
        return true;
    }
    
    /**
     * Update last activity time
     */
    public static function updateActivity() {
        self::set('last_activity', time());
    }
    
    /**
     * Get session info for debugging
     */
    public static function getInfo() {
        return [
            'session_id' => session_id(),
            'session_name' => session_name(),
            'session_status' => session_status(),
            'cookie_params' => session_get_cookie_params(),
            'is_logged_in' => self::isLoggedIn(),
            'user_id' => self::getUserId(),
            'username' => self::getUsername(),
            'user_role' => self::getUserRole(),
            'login_time' => self::get('login_time'),
            'last_activity' => self::get('last_activity')
        ];
    }
}

// Auto-initialize session when this file is included
SessionManager::init();
?>
