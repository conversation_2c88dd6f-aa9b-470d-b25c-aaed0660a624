<?php
/**
 * Database Initialization Script
 * This script will create all required tables and insert default data
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🗄️ Database Initialization</h1>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'arabic_news';

try {
    // First, connect without specifying database to create it
    echo "<h2>Step 1: Creating Database</h2>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '$dbname' created successfully<br>";
    
    // Now connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Step 2: Creating Tables</h2>";
    
    // Create users table
    echo "Creating users table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS users");
    $pdo->exec("CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'editor', 'user') DEFAULT 'user',
        remember_token VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Users table created<br>";
    
    // Create categories table
    echo "Creating categories table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS categories");
    $pdo->exec("CREATE TABLE categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Categories table created<br>";
    
    // Create tags table
    echo "Creating tags table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS tags");
    $pdo->exec("CREATE TABLE tags (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        slug VARCHAR(50) UNIQUE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Tags table created<br>";
    
    // Create articles table
    echo "Creating articles table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS articles");
    $pdo->exec("CREATE TABLE articles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        content LONGTEXT NOT NULL,
        excerpt TEXT,
        featured_image VARCHAR(255),
        category_id INT,
        author_id INT,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        is_breaking BOOLEAN DEFAULT FALSE,
        views INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        published_at TIMESTAMP NULL,
        INDEX idx_status (status),
        INDEX idx_category (category_id),
        INDEX idx_author (author_id),
        INDEX idx_created (created_at),
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Articles table created<br>";
    
    // Create article_tags table
    echo "Creating article_tags table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS article_tags");
    $pdo->exec("CREATE TABLE article_tags (
        article_id INT,
        tag_id INT,
        PRIMARY KEY (article_id, tag_id),
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Article_tags table created<br>";
    
    // Create comments table
    echo "Creating comments table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS comments");
    $pdo->exec("CREATE TABLE comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        article_id INT NOT NULL,
        user_id INT,
        author_name VARCHAR(100),
        author_email VARCHAR(100),
        content TEXT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_article (article_id),
        INDEX idx_status (status),
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Comments table created<br>";
    
    // Create rss_feeds table
    echo "Creating rss_feeds table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS rss_feeds");
    $pdo->exec("CREATE TABLE rss_feeds (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        url VARCHAR(255) NOT NULL,
        category_id INT,
        is_active BOOLEAN DEFAULT TRUE,
        last_fetched TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ RSS_feeds table created<br>";
    
    // Create matches table
    echo "Creating matches table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS matches");
    $pdo->exec("CREATE TABLE matches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        home_team VARCHAR(100) NOT NULL,
        away_team VARCHAR(100) NOT NULL,
        home_score INT DEFAULT NULL,
        away_score INT DEFAULT NULL,
        match_date DATETIME NOT NULL,
        league VARCHAR(100),
        status ENUM('scheduled', 'live', 'finished') DEFAULT 'scheduled',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_date (match_date),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Matches table created<br>";
    
    // Create settings table
    echo "Creating settings table...<br>";
    $pdo->exec("DROP TABLE IF EXISTS settings");
    $pdo->exec("CREATE TABLE settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Settings table created<br>";
    
    echo "<h2>Step 3: Inserting Default Data</h2>";
    
    // Insert default admin user
    echo "Creating admin user...<br>";
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'admin']);
    echo "✅ Admin user created (username: admin, password: admin123)<br>";
    
    // Insert default categories
    echo "Creating default categories...<br>";
    $categories = [
        ['أخبار عامة', 'general-news', 'الأخبار العامة والمتنوعة'],
        ['رياضة', 'sports', 'الأخبار الرياضية'],
        ['تكنولوجيا', 'technology', 'أخبار التكنولوجيا والتقنية'],
        ['اقتصاد', 'economy', 'الأخبار الاقتصادية'],
        ['سياسة', 'politics', 'الأخبار السياسية'],
        ['صحة', 'health', 'أخبار الصحة والطب'],
        ['ثقافة', 'culture', 'الأخبار الثقافية والفنية']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
    foreach ($categories as $category) {
        $stmt->execute($category);
    }
    echo "✅ Default categories created<br>";
    
    // Insert default settings
    echo "Creating default settings...<br>";
    $settings = [
        ['site_name', 'الموقع الإخباري العربي'],
        ['site_description', 'موقع إخباري عربي شامل'],
        ['site_logo', ''],
        ['breaking_news_enabled', '1'],
        ['comments_enabled', '1'],
        ['rss_update_interval', '30'],
        ['articles_per_page', '10'],
        ['site_email', '<EMAIL>'],
        ['site_phone', ''],
        ['site_address', '']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "✅ Default settings created<br>";
    
    // Insert sample tags
    echo "Creating sample tags...<br>";
    $tags = [
        ['عاجل', 'urgent'],
        ['مهم', 'important'],
        ['حصري', 'exclusive'],
        ['تحليل', 'analysis'],
        ['تقرير', 'report']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO tags (name, slug) VALUES (?, ?)");
    foreach ($tags as $tag) {
        $stmt->execute($tag);
    }
    echo "✅ Sample tags created<br>";
    
    // Insert sample matches
    echo "Creating sample matches...<br>";
    $matches = [
        ['الأهلي', 'الزمالك', null, null, date('Y-m-d H:i:s', strtotime('+1 day')), 'الدوري المصري', 'scheduled'],
        ['ريال مدريد', 'برشلونة', null, null, date('Y-m-d H:i:s', strtotime('+2 days')), 'الليغا الإسبانية', 'scheduled'],
        ['مانشستر يونايتد', 'ليفربول', 2, 1, date('Y-m-d H:i:s', strtotime('-1 day')), 'الدوري الإنجليزي', 'finished']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO matches (home_team, away_team, home_score, away_score, match_date, league, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
    foreach ($matches as $match) {
        $stmt->execute($match);
    }
    echo "✅ Sample matches created<br>";
    
    // Insert sample article
    echo "Creating sample article...<br>";
    $sampleArticle = [
        'مرحباً بكم في الموقع الإخباري العربي',
        'welcome-to-arabic-news',
        'هذا مقال ترحيبي في الموقع الإخباري العربي الجديد. يمكنكم من خلال هذا الموقع متابعة آخر الأخبار والمستجدات في جميع المجالات.',
        'مقال ترحيبي في الموقع الإخباري العربي الجديد',
        null,
        1, // category_id (أخبار عامة)
        1, // author_id (admin)
        'published',
        0, // not breaking
        0, // views
        date('Y-m-d H:i:s'),
        date('Y-m-d H:i:s')
    ];
    
    $stmt = $pdo->prepare("INSERT INTO articles (title, slug, content, excerpt, featured_image, category_id, author_id, status, is_breaking, views, created_at, published_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute($sampleArticle);
    echo "✅ Sample article created<br>";
    
    echo "<h2>Step 4: Verification</h2>";
    
    // Verify all tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $expectedTables = ['users', 'categories', 'tags', 'articles', 'article_tags', 'comments', 'rss_feeds', 'matches', 'settings'];
    
    echo "Tables in database:<br>";
    foreach ($expectedTables as $table) {
        if (in_array($table, $tables)) {
            echo "✅ $table<br>";
        } else {
            echo "❌ $table (MISSING)<br>";
        }
    }
    
    // Count records in each table
    echo "<br>Record counts:<br>";
    foreach ($expectedTables as $table) {
        if (in_array($table, $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "📊 $table: $count records<br>";
        }
    }
    
    // Test admin login
    echo "<br>Testing admin user:<br>";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user exists<br>";
        echo "📧 Email: " . $admin['email'] . "<br>";
        echo "👤 Role: " . $admin['role'] . "<br>";
        
        // Test password
        if (password_verify('admin123', $admin['password'])) {
            echo "✅ Password verification successful<br>";
        } else {
            echo "❌ Password verification failed<br>";
        }
    } else {
        echo "❌ Admin user not found<br>";
    }
    
    // Mark setup as complete
    file_put_contents('config/setup_complete.txt', date('Y-m-d H:i:s') . " - Database initialized successfully");
    
    echo "<h2>🎉 Database Initialization Complete!</h2>";
    echo "<p>✅ All tables created successfully</p>";
    echo "<p>✅ Default data inserted</p>";
    echo "<p>✅ Admin user ready</p>";
    
    echo "<h3>Login Credentials:</h3>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> admin</li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<p><a href='index.php' style='background: #1e40af; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Visit Website</a></p>";
    echo "<p><a href='admin/' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>⚙️ Admin Panel</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>❌ Database Error</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure MySQL is running in XAMPP</li>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "<li>Ensure MySQL user has CREATE privileges</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2, h3 {
    color: #1e40af;
}
</style>
