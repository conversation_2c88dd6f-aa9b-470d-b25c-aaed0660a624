// Main JavaScript for Arabic News Website

document.addEventListener('DOMContentLoaded', function() {
    
    // Scroll to Top Button
    const scrollToTopBtn = document.getElementById('scrollToTop');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('show');
        } else {
            scrollToTopBtn.classList.remove('show');
        }
    });
    
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }, 5000);
    });
    
    // Search form enhancement
    const searchForm = document.querySelector('.search-form');
    const searchInput = document.querySelector('.search-input');
    
    if (searchForm && searchInput) {
        // Add search suggestions (you can implement this with AJAX)
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                // Implement search suggestions here
                // fetchSearchSuggestions(query);
            }
        });
        
        // Clear search on escape key
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                this.blur();
            }
        });
    }
    
    // Breaking news ticker pause on hover
    const tickerText = document.getElementById('breakingNewsText');
    if (tickerText) {
        tickerText.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
        });
        
        tickerText.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
        });
    }
    
    // Image lazy loading
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
    
    // Article view tracking
    function trackArticleView(articleId) {
        if (articleId) {
            fetch('/api/track-view.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ article_id: articleId })
            }).catch(error => {
                console.log('View tracking failed:', error);
            });
        }
    }
    
    // Track article view if on article page
    const articleId = document.querySelector('[data-article-id]');
    if (articleId) {
        // Track view after 10 seconds of reading
        setTimeout(() => {
            trackArticleView(articleId.dataset.articleId);
        }, 10000);
    }
    
    // Social sharing
    function shareArticle(platform, url, title) {
        const encodedUrl = encodeURIComponent(url);
        const encodedTitle = encodeURIComponent(title);
        let shareUrl = '';
        
        switch (platform) {
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`;
                break;
            case 'whatsapp':
                shareUrl = `https://wa.me/?text=${encodedTitle} ${encodedUrl}`;
                break;
            case 'telegram':
                shareUrl = `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`;
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    }
    
    // Add event listeners for share buttons
    document.querySelectorAll('.share-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const platform = this.dataset.platform;
            const url = this.dataset.url || window.location.href;
            const title = this.dataset.title || document.title;
            shareArticle(platform, url, title);
        });
    });
    
    // Form validation enhancement
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const inputs = form.querySelectorAll('input[required], textarea[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
                
                // Email validation
                if (input.type === 'email' && input.value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(input.value)) {
                        input.classList.add('is-invalid');
                        isValid = false;
                    }
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
        });
    });
    
    // Auto-refresh breaking news
    function refreshBreakingNews() {
        fetch('/api/breaking-news.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.news) {
                    const tickerText = document.getElementById('breakingNewsText');
                    if (tickerText) {
                        tickerText.innerHTML = data.news;
                    }
                }
            })
            .catch(error => {
                console.log('Breaking news refresh failed:', error);
            });
    }
    
    // Refresh breaking news every 5 minutes
    setInterval(refreshBreakingNews, 5 * 60 * 1000);
    
    // Dark mode toggle (if implemented)
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            const isDark = document.body.classList.contains('dark-mode');
            localStorage.setItem('darkMode', isDark);
        });
        
        // Load saved dark mode preference
        const savedDarkMode = localStorage.getItem('darkMode');
        if (savedDarkMode === 'true') {
            document.body.classList.add('dark-mode');
        }
    }
    
    // Infinite scroll for article lists
    let isLoading = false;
    let currentPage = 1;
    
    function loadMoreArticles() {
        if (isLoading) return;
        
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        const articlesContainer = document.getElementById('articlesContainer');
        
        if (!loadMoreBtn || !articlesContainer) return;
        
        isLoading = true;
        loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        
        fetch(`/api/load-more-articles.php?page=${currentPage + 1}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.articles) {
                    articlesContainer.insertAdjacentHTML('beforeend', data.articles);
                    currentPage++;
                    
                    if (!data.hasMore) {
                        loadMoreBtn.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.log('Load more failed:', error);
            })
            .finally(() => {
                isLoading = false;
                loadMoreBtn.innerHTML = '<i class="fas fa-plus"></i> تحميل المزيد';
            });
    }
    
    // Add event listener for load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreArticles);
    }
    
    // Auto-load more on scroll (optional)
    window.addEventListener('scroll', function() {
        if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            if (loadMoreBtn && loadMoreBtn.style.display !== 'none') {
                loadMoreArticles();
            }
        }
    });
    
    // Copy to clipboard functionality
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            showToast('تم نسخ الرابط بنجاح!', 'success');
        }).catch(function(err) {
            console.error('Copy failed:', err);
            showToast('فشل في نسخ الرابط', 'error');
        });
    }
    
    // Toast notification system
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
    
    // Initialize tooltips (if using Bootstrap)
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers (if using Bootstrap)
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Add fade-in animation to elements when they come into view
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all article cards
    document.querySelectorAll('.article-card').forEach(card => {
        observer.observe(card);
    });

    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData.loadEventEnd - perfData.loadEventStart > 3000) {
                    console.log('Page load time is slow:', perfData.loadEventEnd - perfData.loadEventStart);
                }
            }, 0);
        });
    }

    // Preload critical resources
    function preloadCriticalResources() {
        const criticalImages = document.querySelectorAll('img[data-critical]');
        criticalImages.forEach(img => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = img.src;
            document.head.appendChild(link);
        });
    }

    preloadCriticalResources();

    // Initialize new features
    initThemeToggle();
    initAdvancedSearch();
    initNewsletterSubscription();
    initBookmarkSystem();
    initReadingProgress();
    initInfiniteScroll();
    initSocialSharing();

});

// Theme Toggle Functionality
function initThemeToggle() {
    // Create theme toggle button
    const themeToggle = document.createElement('button');
    themeToggle.className = 'theme-toggle';
    themeToggle.innerHTML = '<i class="fas fa-moon"></i><i class="fas fa-sun"></i>';
    themeToggle.setAttribute('aria-label', 'تبديل الوضع المظلم/المضيء');
    document.body.appendChild(themeToggle);

    // Load saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);

    // Theme toggle event
    themeToggle.addEventListener('click', function() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Show toast notification
        showToast(newTheme === 'dark' ? 'تم تفعيل الوضع المظلم' : 'تم تفعيل الوضع المضيء', 'success');
    });
}

// Advanced Search Functionality
function initAdvancedSearch() {
    // Create advanced search modal
    const modal = document.createElement('div');
    modal.className = 'advanced-search-modal';
    modal.innerHTML = `
        <div class="advanced-search-content">
            <div class="advanced-search-header">
                <h3 class="advanced-search-title">البحث المتقدم</h3>
                <button class="close-advanced-search" aria-label="إغلاق">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="advancedSearchForm">
                <div class="search-filter-group">
                    <label class="search-filter-label">الكلمات المفتاحية</label>
                    <input type="text" class="search-filter-input" name="keywords" placeholder="ابحث في العناوين والمحتوى...">
                </div>
                <div class="search-filter-group">
                    <label class="search-filter-label">الفئة</label>
                    <select class="search-filter-select" name="category">
                        <option value="">جميع الفئات</option>
                        <option value="politics">السياسة</option>
                        <option value="sports">الرياضة</option>
                        <option value="technology">التكنولوجيا</option>
                        <option value="economy">الاقتصاد</option>
                    </select>
                </div>
                <div class="search-filter-group">
                    <label class="search-filter-label">الكاتب</label>
                    <input type="text" class="search-filter-input" name="author" placeholder="اسم الكاتب...">
                </div>
                <div class="search-filter-group">
                    <label class="search-filter-label">نطاق التاريخ</label>
                    <div class="date-range-inputs">
                        <input type="date" class="search-filter-input" name="date_from" placeholder="من تاريخ">
                        <input type="date" class="search-filter-input" name="date_to" placeholder="إلى تاريخ">
                    </div>
                </div>
                <div class="search-actions">
                    <button type="button" class="btn-clear-filters">مسح الفلاتر</button>
                    <button type="submit" class="btn-search-advanced">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>
    `;
    document.body.appendChild(modal);

    // Add advanced search trigger to existing search
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        const advancedBtn = document.createElement('button');
        advancedBtn.type = 'button';
        advancedBtn.className = 'btn btn-outline-primary ms-2';
        advancedBtn.innerHTML = '<i class="fas fa-filter"></i>';
        advancedBtn.title = 'البحث المتقدم';
        searchForm.appendChild(advancedBtn);

        advancedBtn.addEventListener('click', () => {
            modal.classList.add('show');
        });
    }

    // Close modal events
    modal.querySelector('.close-advanced-search').addEventListener('click', () => {
        modal.classList.remove('show');
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    });

    // Clear filters
    modal.querySelector('.btn-clear-filters').addEventListener('click', () => {
        modal.querySelector('#advancedSearchForm').reset();
    });

    // Handle advanced search form
    modal.querySelector('#advancedSearchForm').addEventListener('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const searchParams = Object.fromEntries(formData.entries());

        // Perform advanced search
        performAdvancedSearch(searchParams);
        modal.classList.remove('show');
    });
}

// Newsletter Subscription
function initNewsletterSubscription() {
    // Add newsletter widget to sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        const newsletterWidget = document.createElement('div');
        newsletterWidget.className = 'newsletter-widget';
        newsletterWidget.innerHTML = `
            <h4 class="newsletter-title">اشترك في النشرة الإخبارية</h4>
            <p class="newsletter-description">احصل على آخر الأخبار مباشرة في بريدك الإلكتروني</p>
            <form class="newsletter-form" id="newsletterForm">
                <input type="email" class="newsletter-input" placeholder="بريدك الإلكتروني..." required>
                <button type="submit" class="newsletter-submit">اشتراك</button>
            </form>
        `;
        sidebar.insertBefore(newsletterWidget, sidebar.firstChild);

        // Handle newsletter subscription
        newsletterWidget.querySelector('#newsletterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            const email = e.target.querySelector('.newsletter-input').value;

            if (validateEmail(email)) {
                // Simulate subscription
                showToast('تم الاشتراك بنجاح! شكراً لك', 'success');
                e.target.reset();
            } else {
                showToast('يرجى إدخال بريد إلكتروني صحيح', 'error');
            }
        });
    }
}

// Email validation
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Bookmark System
function initBookmarkSystem() {
    // Add bookmark buttons to article cards
    const articleCards = document.querySelectorAll('.article-card');
    articleCards.forEach(card => {
        const bookmarkBtn = document.createElement('button');
        bookmarkBtn.className = 'bookmark-btn';
        bookmarkBtn.innerHTML = '<i class="far fa-bookmark"></i>';
        bookmarkBtn.setAttribute('aria-label', 'إضافة إلى المفضلة');

        const articleId = card.dataset.articleId || Math.random().toString(36).substr(2, 9);
        card.dataset.articleId = articleId;

        // Check if already bookmarked
        const bookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');
        if (bookmarks.includes(articleId)) {
            bookmarkBtn.classList.add('bookmarked');
            bookmarkBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
        }

        bookmarkBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            toggleBookmark(articleId, bookmarkBtn);
        });

        card.style.position = 'relative';
        card.appendChild(bookmarkBtn);
    });
}

function toggleBookmark(articleId, button) {
    let bookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');

    if (bookmarks.includes(articleId)) {
        // Remove bookmark
        bookmarks = bookmarks.filter(id => id !== articleId);
        button.classList.remove('bookmarked');
        button.innerHTML = '<i class="far fa-bookmark"></i>';
        showToast('تم إزالة المقال من المفضلة', 'info');
    } else {
        // Add bookmark
        bookmarks.push(articleId);
        button.classList.add('bookmarked');
        button.innerHTML = '<i class="fas fa-bookmark"></i>';
        showToast('تم إضافة المقال إلى المفضلة', 'success');
    }

    localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
}

// Reading Progress Indicator
function initReadingProgress() {
    // Only on article pages
    if (document.querySelector('.article-full')) {
        const progressBar = document.createElement('div');
        progressBar.className = 'reading-progress';
        progressBar.innerHTML = '<div class="reading-progress-bar"></div>';
        document.body.appendChild(progressBar);

        const progressBarFill = progressBar.querySelector('.reading-progress-bar');

        window.addEventListener('scroll', () => {
            const article = document.querySelector('.article-content-full');
            if (article) {
                const articleTop = article.offsetTop;
                const articleHeight = article.offsetHeight;
                const windowHeight = window.innerHeight;
                const scrollTop = window.pageYOffset;

                const articleStart = articleTop - windowHeight / 2;
                const articleEnd = articleTop + articleHeight - windowHeight / 2;

                if (scrollTop >= articleStart && scrollTop <= articleEnd) {
                    progressBar.classList.add('show');
                    const progress = ((scrollTop - articleStart) / (articleEnd - articleStart)) * 100;
                    progressBarFill.style.width = Math.min(Math.max(progress, 0), 100) + '%';
                } else {
                    progressBar.classList.remove('show');
                }
            }
        });
    }
}

// Infinite Scroll
function initInfiniteScroll() {
    let loading = false;
    let page = 1;
    const articlesContainer = document.getElementById('articlesContainer');

    if (articlesContainer) {
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'infinite-scroll-loading';
        loadingIndicator.style.display = 'none';
        loadingIndicator.innerHTML = `
            <div class="infinite-scroll-spinner"></div>
            <div class="infinite-scroll-text">جاري تحميل المزيد من المقالات...</div>
        `;
        articlesContainer.parentNode.appendChild(loadingIndicator);

        window.addEventListener('scroll', () => {
            if (loading) return;

            const scrollTop = window.pageYOffset;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            if (scrollTop + windowHeight >= documentHeight - 1000) {
                loadMoreArticles();
            }
        });

        function loadMoreArticles() {
            loading = true;
            loadingIndicator.style.display = 'block';
            page++;

            // Simulate API call
            setTimeout(() => {
                // Create skeleton loaders first
                for (let i = 0; i < 4; i++) {
                    const skeleton = createSkeletonCard();
                    articlesContainer.appendChild(skeleton);
                }

                // Replace skeletons with real content after delay
                setTimeout(() => {
                    const skeletons = articlesContainer.querySelectorAll('.skeleton-article-card');
                    skeletons.forEach((skeleton, index) => {
                        const realCard = createArticleCard({
                            title: `مقال جديد ${page}-${index + 1}`,
                            excerpt: 'هذا نص تجريبي للمقال الجديد...',
                            image: 'assets/images/placeholder.jpg',
                            category: 'عام',
                            author: 'الكاتب',
                            date: new Date().toISOString(),
                            views: Math.floor(Math.random() * 1000)
                        });
                        skeleton.replaceWith(realCard);
                    });

                    loading = false;
                    loadingIndicator.style.display = 'none';

                    // Re-initialize bookmark system for new articles
                    initBookmarkSystem();
                }, 1500);
            }, 500);
        }
    }
}

// Social Sharing
function initSocialSharing() {
    // Add social sharing widget to sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        const socialWidget = document.createElement('div');
        socialWidget.className = 'social-sharing-widget';
        socialWidget.innerHTML = `
            <h4 class="social-sharing-title">شارك الموقع</h4>
            <div class="social-sharing-buttons">
                <a href="#" class="social-share-btn facebook" data-platform="facebook">
                    <i class="fab fa-facebook-f"></i>
                    فيسبوك
                    <span class="share-count">1.2k</span>
                </a>
                <a href="#" class="social-share-btn twitter" data-platform="twitter">
                    <i class="fab fa-twitter"></i>
                    تويتر
                    <span class="share-count">856</span>
                </a>
                <a href="#" class="social-share-btn whatsapp" data-platform="whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    واتساب
                    <span class="share-count">2.1k</span>
                </a>
                <a href="#" class="social-share-btn telegram" data-platform="telegram">
                    <i class="fab fa-telegram"></i>
                    تلجرام
                    <span class="share-count">634</span>
                </a>
            </div>
        `;

        // Insert after newsletter widget or at beginning
        const newsletterWidget = sidebar.querySelector('.newsletter-widget');
        if (newsletterWidget) {
            newsletterWidget.insertAdjacentElement('afterend', socialWidget);
        } else {
            sidebar.insertBefore(socialWidget, sidebar.firstChild);
        }

        // Handle social sharing
        socialWidget.querySelectorAll('.social-share-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const platform = btn.dataset.platform;
                const url = encodeURIComponent(window.location.href);
                const title = encodeURIComponent(document.title);

                let shareUrl = '';
                switch (platform) {
                    case 'facebook':
                        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                        break;
                    case 'twitter':
                        shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
                        break;
                    case 'whatsapp':
                        shareUrl = `https://wa.me/?text=${title} ${url}`;
                        break;
                    case 'telegram':
                        shareUrl = `https://t.me/share/url?url=${url}&text=${title}`;
                        break;
                }

                if (shareUrl) {
                    window.open(shareUrl, '_blank', 'width=600,height=400');

                    // Update share count (simulate)
                    const countElement = btn.querySelector('.share-count');
                    const currentCount = parseInt(countElement.textContent.replace(/[^\d]/g, ''));
                    countElement.textContent = formatNumber(currentCount + 1);
                }
            });
        });
    }
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'منذ لحظات';
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    if (days < 30) return `منذ ${days} يوم`;
    
    return date.toLocaleDateString('ar-SA');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Helper functions for new features
function createSkeletonCard() {
    const skeleton = document.createElement('div');
    skeleton.className = 'col-md-6 mb-4';
    skeleton.innerHTML = `
        <div class="skeleton-article-card skeleton">
            <div class="skeleton-image skeleton"></div>
            <div class="skeleton-content">
                <div class="skeleton-title skeleton"></div>
                <div class="skeleton-title short skeleton"></div>
                <div class="skeleton-text skeleton"></div>
                <div class="skeleton-text skeleton"></div>
                <div class="skeleton-text short skeleton"></div>
                <div class="skeleton-meta">
                    <div class="skeleton-meta-item skeleton"></div>
                    <div class="skeleton-meta-item skeleton"></div>
                </div>
            </div>
        </div>
    `;
    return skeleton;
}

function createArticleCard(article) {
    const card = document.createElement('div');
    card.className = 'col-md-6 mb-4 fade-in';
    card.innerHTML = `
        <div class="article-card" data-article-id="${Math.random().toString(36).substr(2, 9)}">
            <img src="${article.image}" alt="${article.title}" class="article-image" loading="lazy">
            <div class="article-content">
                <div class="article-category-wrapper mb-2">
                    <a href="#" class="article-category">${article.category}</a>
                </div>
                <h4 class="article-title">
                    <a href="#">${article.title}</a>
                </h4>
                <p class="article-excerpt">${article.excerpt}</p>
                <div class="article-meta">
                    <div class="meta-left">
                        <span><i class="fas fa-user"></i> ${article.author}</span>
                        <span><i class="fas fa-clock"></i> ${formatDate(article.date)}</span>
                    </div>
                    <div class="meta-right">
                        <span><i class="fas fa-eye"></i> ${formatNumber(article.views)}</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    return card;
}

function performAdvancedSearch(params) {
    // Show loading state
    const articlesContainer = document.getElementById('articlesContainer');
    if (articlesContainer) {
        // Clear current articles
        articlesContainer.innerHTML = '';

        // Show skeleton loaders
        for (let i = 0; i < 6; i++) {
            const skeleton = createSkeletonCard();
            articlesContainer.appendChild(skeleton);
        }

        // Simulate search API call
        setTimeout(() => {
            articlesContainer.innerHTML = '';

            // Create mock search results
            const searchResults = [
                {
                    title: `نتائج البحث عن: ${params.keywords || 'جميع المقالات'}`,
                    excerpt: 'هذه نتائج البحث المتقدم بناءً على المعايير المحددة...',
                    image: 'assets/images/placeholder.jpg',
                    category: params.category || 'عام',
                    author: params.author || 'الكاتب',
                    date: new Date().toISOString(),
                    views: Math.floor(Math.random() * 1000)
                }
            ];

            searchResults.forEach(result => {
                const card = createArticleCard(result);
                articlesContainer.appendChild(card);
            });

            // Re-initialize features for new content
            initBookmarkSystem();

            showToast('تم العثور على النتائج', 'success');
        }, 1500);
    }
}

function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function timeAgo(dateString) {
    return formatDate(dateString);
}

function truncateText(text, length) {
    if (text.length <= length) return text;
    return text.substr(0, length) + '...';
}
