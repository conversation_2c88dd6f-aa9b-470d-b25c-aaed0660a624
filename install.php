<?php
/**
 * Arabic News Website - Installation Script
 * This script helps set up the website for the first time
 */

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('الموقع مثبت بالفعل. احذف ملف config/installed.lock لإعادة التثبيت.');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // Database configuration
            $host = $_POST['host'] ?? 'localhost';
            $dbname = $_POST['dbname'] ?? 'arabic_news';
            $username = $_POST['username'] ?? 'root';
            $password = $_POST['password'] ?? '';
            
            try {
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Create database
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                
                // Update config file
                $configContent = file_get_contents('config/database.php');
                $configContent = str_replace("'localhost'", "'$host'", $configContent);
                $configContent = str_replace("'arabic_news'", "'$dbname'", $configContent);
                $configContent = str_replace("'root'", "'$username'", $configContent);
                $configContent = str_replace("''", "'$password'", $configContent);
                
                file_put_contents('config/database.php', $configContent);
                
                $success = 'تم إعداد قاعدة البيانات بنجاح!';
                $step = 3;
            } catch (PDOException $e) {
                $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // Admin user setup
            $adminUsername = $_POST['admin_username'] ?? '';
            $adminEmail = $_POST['admin_email'] ?? '';
            $adminPassword = $_POST['admin_password'] ?? '';
            
            if (empty($adminUsername) || empty($adminEmail) || empty($adminPassword)) {
                $error = 'جميع الحقول مطلوبة';
            } else {
                try {
                    require_once 'config/database.php';
                    $database = new Database();
                    $pdo = $database->connect();
                    
                    // Update admin user
                    $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ? WHERE id = 1");
                    $stmt->execute([$adminUsername, $adminEmail, $hashedPassword]);
                    
                    $success = 'تم إعداد المدير بنجاح!';
                    $step = 4;
                } catch (Exception $e) {
                    $error = 'خطأ في إعداد المدير: ' . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // Site settings
            $siteName = $_POST['site_name'] ?? 'الموقع الإخباري العربي';
            $siteDescription = $_POST['site_description'] ?? 'موقع إخباري عربي شامل';
            
            try {
                require_once 'config/database.php';
                $database = new Database();
                $pdo = $database->connect();
                
                // Update site settings
                $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = 'site_name'");
                $stmt->execute([$siteName]);
                
                $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = 'site_description'");
                $stmt->execute([$siteDescription]);
                
                // Create installation lock file
                file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
                
                $success = 'تم تثبيت الموقع بنجاح!';
                $step = 5;
            } catch (Exception $e) {
                $error = 'خطأ في حفظ الإعدادات: ' . $e->getMessage();
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت الموقع الإخباري العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .install-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .install-body {
            padding: 40px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            background: #e5e7eb;
            color: #6b7280;
            font-weight: 600;
        }
        .step.active {
            background: #1e40af;
            color: white;
        }
        .step.completed {
            background: #059669;
            color: white;
        }
        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #1e40af;
            box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
        }
        .btn-primary {
            background: #1e40af;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
        }
        .btn-primary:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="install-container">
                    <div class="install-header">
                        <h1><i class="fas fa-newspaper"></i> الموقع الإخباري العربي</h1>
                        <p>معالج التثبيت</p>
                    </div>
                    
                    <div class="install-body">
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                            <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                            <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                            <div class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : ''; ?>">4</div>
                            <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5</div>
                        </div>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($step == 1): ?>
                            <!-- Welcome Step -->
                            <div class="text-center">
                                <h3>مرحباً بك في معالج التثبيت</h3>
                                <p class="text-muted">سنقوم بإعداد موقعك الإخباري في خطوات بسيطة</p>
                                
                                <div class="my-4">
                                    <h5>المتطلبات:</h5>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> PHP 7.4+</li>
                                        <li><i class="fas fa-check text-success"></i> MySQL 5.7+</li>
                                        <li><i class="fas fa-check text-success"></i> Apache/Nginx</li>
                                        <li><i class="fas fa-check text-success"></i> GD Library</li>
                                    </ul>
                                </div>
                                
                                <a href="?step=2" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-left"></i>
                                    البدء في التثبيت
                                </a>
                            </div>

                        <?php elseif ($step == 2): ?>
                            <!-- Database Configuration -->
                            <h3>إعداد قاعدة البيانات</h3>
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">خادم قاعدة البيانات</label>
                                        <input type="text" name="host" class="form-control" value="localhost" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم قاعدة البيانات</label>
                                        <input type="text" name="dbname" class="form-control" value="arabic_news" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" name="username" class="form-control" value="root" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" name="password" class="form-control">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-database"></i>
                                    اختبار الاتصال وإنشاء قاعدة البيانات
                                </button>
                            </form>

                        <?php elseif ($step == 3): ?>
                            <!-- Admin User Setup -->
                            <h3>إعداد حساب المدير</h3>
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" name="admin_username" class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" name="admin_email" class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" name="admin_password" class="form-control" required>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-user-shield"></i>
                                    إنشاء حساب المدير
                                </button>
                            </form>

                        <?php elseif ($step == 4): ?>
                            <!-- Site Settings -->
                            <h3>إعدادات الموقع</h3>
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم الموقع</label>
                                    <input type="text" name="site_name" class="form-control" value="الموقع الإخباري العربي" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">وصف الموقع</label>
                                    <textarea name="site_description" class="form-control" rows="3" required>موقع إخباري عربي شامل</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ الإعدادات وإنهاء التثبيت
                                </button>
                            </form>

                        <?php elseif ($step == 5): ?>
                            <!-- Installation Complete -->
                            <div class="text-center">
                                <div class="mb-4">
                                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                </div>
                                <h3>تم التثبيت بنجاح!</h3>
                                <p class="text-muted">موقعك الإخباري جاهز للاستخدام</p>
                                
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h5>الموقع الرئيسي</h5>
                                                <a href="index.php" class="btn btn-outline-primary">
                                                    <i class="fas fa-home"></i>
                                                    زيارة الموقع
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h5>لوحة التحكم</h5>
                                                <a href="admin/" class="btn btn-primary">
                                                    <i class="fas fa-tachometer-alt"></i>
                                                    لوحة التحكم
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info mt-4">
                                    <strong>ملاحظة:</strong> احذف ملف install.php من الخادم لأسباب أمنية.
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
