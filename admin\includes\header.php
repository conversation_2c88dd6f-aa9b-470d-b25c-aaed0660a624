<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Admin CSS -->
    <link href="<?php echo SITE_URL; ?>/admin/assets/css/admin.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="admin-logo">
                        <a href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="admin-search">
                        <form class="search-form">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="البحث في المحتوى...">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="admin-user-menu">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                                <?php echo SessionManager::getUsername(); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/" target="_blank">
                                    <i class="fas fa-globe"></i> عرض الموقع
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/profile.php">
                                    <i class="fas fa-user-edit"></i> الملف الشخصي
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/settings.php">
                                    <i class="fas fa-cog"></i> الإعدادات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php">
                                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/admin/" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#" class="nav-link has-submenu" data-bs-toggle="collapse" data-bs-target="#articlesMenu">
                        <i class="fas fa-newspaper"></i>
                        <span>المقالات</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </a>
                    <ul class="submenu collapse" id="articlesMenu">
                        <li><a href="<?php echo SITE_URL; ?>/admin/articles/" class="submenu-link">
                            <i class="fas fa-list"></i> جميع المقالات
                        </a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/articles/create.php" class="submenu-link">
                            <i class="fas fa-plus"></i> مقال جديد
                        </a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/categories/" class="submenu-link">
                            <i class="fas fa-tags"></i> الأقسام
                        </a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/tags/" class="submenu-link">
                            <i class="fas fa-hashtag"></i> الوسوم
                        </a></li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/admin/comments/" class="nav-link">
                        <i class="fas fa-comments"></i>
                        <span>التعليقات</span>
                        <?php
                        try {
                            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM comments WHERE status = 'pending'");
                            $stmt->execute();
                            $pendingCount = $stmt->fetch()['count'];
                            if ($pendingCount > 0) {
                                echo '<span class="badge bg-warning">' . $pendingCount . '</span>';
                            }
                        } catch(PDOException $e) {
                            // Handle error silently
                        }
                        ?>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#" class="nav-link has-submenu" data-bs-toggle="collapse" data-bs-target="#rssMenu">
                        <i class="fas fa-rss"></i>
                        <span>RSS</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </a>
                    <ul class="submenu collapse" id="rssMenu">
                        <li><a href="<?php echo SITE_URL; ?>/admin/rss/" class="submenu-link">
                            <i class="fas fa-list"></i> إدارة المصادر
                        </a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/rss/fetch.php" class="submenu-link">
                            <i class="fas fa-sync"></i> تحديث الآن
                        </a></li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/admin/matches/" class="nav-link">
                        <i class="fas fa-futbol"></i>
                        <span>المباريات</span>
                    </a>
                </li>
                
                <?php if (isAdmin()): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link has-submenu" data-bs-toggle="collapse" data-bs-target="#usersMenu">
                        <i class="fas fa-users"></i>
                        <span>المستخدمون</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </a>
                    <ul class="submenu collapse" id="usersMenu">
                        <li><a href="<?php echo SITE_URL; ?>/admin/users/" class="submenu-link">
                            <i class="fas fa-list"></i> جميع المستخدمين
                        </a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/users/create.php" class="submenu-link">
                            <i class="fas fa-user-plus"></i> مستخدم جديد
                        </a></li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/admin/settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#" class="nav-link has-submenu" data-bs-toggle="collapse" data-bs-target="#toolsMenu">
                        <i class="fas fa-tools"></i>
                        <span>الأدوات</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </a>
                    <ul class="submenu collapse" id="toolsMenu">
                        <li><a href="<?php echo SITE_URL; ?>/admin/backup.php" class="submenu-link">
                            <i class="fas fa-download"></i> النسخ الاحتياطي
                        </a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/analytics.php" class="submenu-link">
                            <i class="fas fa-chart-bar"></i> التحليلات
                        </a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/logs.php" class="submenu-link">
                            <i class="fas fa-file-alt"></i> السجلات
                        </a></li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="admin-main">
        <div class="container-fluid">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="admin-breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?php echo SITE_URL; ?>/admin/">
                            <i class="fas fa-home"></i>
                            الرئيسية
                        </a>
                    </li>
                    <?php if (isset($pageTitle) && $pageTitle !== 'لوحة التحكم'): ?>
                    <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
                    <?php endif; ?>
                </ol>
            </nav>

            <!-- Page Header -->
            <?php if (isset($pageTitle)): ?>
            <div class="page-header">
                <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                <?php if (isset($pageDescription)): ?>
                <p class="page-description"><?php echo $pageDescription; ?></p>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Alerts -->
            <?php displayAlert(); ?>

            <!-- Page Content -->
