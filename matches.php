<?php
require_once 'config/config.php';

// Get filter parameters
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$league = isset($_GET['league']) ? sanitize($_GET['league']) : '';
$date = isset($_GET['date']) ? sanitize($_GET['date']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$whereConditions = [];
$params = [];

if (!empty($status)) {
    $whereConditions[] = "status = ?";
    $params[] = $status;
}

if (!empty($league)) {
    $whereConditions[] = "league LIKE ?";
    $params[] = '%' . $league . '%';
}

if (!empty($date)) {
    $whereConditions[] = "DATE(match_date) = ?";
    $params[] = $date;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

try {
    // Count total matches
    $countSQL = "SELECT COUNT(*) as total FROM matches $whereClause";
    $stmt = $pdo->prepare($countSQL);
    $stmt->execute($params);
    $totalMatches = $stmt->fetch()['total'];
    $totalPages = ceil($totalMatches / $limit);
    
    // Get matches
    $matchesSQL = "SELECT * FROM matches $whereClause ORDER BY match_date ASC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($matchesSQL);
    $stmt->execute($params);
    $matches = $stmt->fetchAll();
    
    // Get available leagues
    $stmt = $pdo->prepare("SELECT DISTINCT league FROM matches WHERE league IS NOT NULL ORDER BY league");
    $stmt->execute();
    $leagues = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch(PDOException $e) {
    $matches = [];
    $totalMatches = 0;
    $totalPages = 0;
    $leagues = [];
}

$pageTitle = 'جدول المباريات';
$pageDescription = 'تابع جدول مباريات كرة القدم والنتائج المباشرة';

include 'includes/header.php';
?>

<div class="row">
    <!-- Main Content -->
    <div class="col-lg-9">
        <!-- Page Header -->
        <div class="matches-header">
            <h1 class="matches-title">
                <i class="fas fa-futbol"></i>
                جدول المباريات
            </h1>
            <p class="matches-subtitle">تابع آخر مباريات كرة القدم والنتائج المباشرة</p>
        </div>

        <!-- Filters -->
        <div class="matches-filters">
            <form method="GET" class="filters-form">
                <div class="row">
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="scheduled" <?php echo $status === 'scheduled' ? 'selected' : ''; ?>>مجدولة</option>
                            <option value="live" <?php echo $status === 'live' ? 'selected' : ''; ?>>مباشرة</option>
                            <option value="finished" <?php echo $status === 'finished' ? 'selected' : ''; ?>>منتهية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="league" class="form-select">
                            <option value="">جميع البطولات</option>
                            <?php foreach ($leagues as $leagueOption): ?>
                                <option value="<?php echo htmlspecialchars($leagueOption); ?>" 
                                        <?php echo $league === $leagueOption ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($leagueOption); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="date" class="form-control" value="<?php echo htmlspecialchars($date); ?>">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter"></i>
                            تصفية
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Matches List -->
        <?php if (!empty($matches)): ?>
            <div class="matches-list">
                <?php 
                $currentDate = '';
                foreach ($matches as $match): 
                    $matchDate = date('Y-m-d', strtotime($match['match_date']));
                    if ($matchDate !== $currentDate):
                        $currentDate = $matchDate;
                ?>
                    <div class="date-separator">
                        <h4 class="match-date">
                            <i class="fas fa-calendar"></i>
                            <?php echo date('l, d F Y', strtotime($match['match_date'])); ?>
                        </h4>
                    </div>
                <?php endif; ?>

                <div class="match-card <?php echo $match['status']; ?>">
                    <div class="match-info">
                        <div class="match-league">
                            <?php echo htmlspecialchars($match['league']); ?>
                        </div>
                        <div class="match-time">
                            <?php if ($match['status'] === 'live'): ?>
                                <span class="live-indicator">
                                    <i class="fas fa-circle"></i>
                                    مباشر
                                </span>
                            <?php else: ?>
                                <?php echo date('H:i', strtotime($match['match_date'])); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="match-teams">
                        <div class="home-team">
                            <div class="team-name"><?php echo htmlspecialchars($match['home_team']); ?></div>
                            <?php if ($match['status'] === 'finished' && $match['home_score'] !== null): ?>
                                <div class="team-score"><?php echo $match['home_score']; ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="match-vs">
                            <?php if ($match['status'] === 'finished' && $match['home_score'] !== null): ?>
                                <span class="score-separator">-</span>
                            <?php else: ?>
                                <span class="vs-text">VS</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="away-team">
                            <div class="team-name"><?php echo htmlspecialchars($match['away_team']); ?></div>
                            <?php if ($match['status'] === 'finished' && $match['away_score'] !== null): ?>
                                <div class="team-score"><?php echo $match['away_score']; ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="match-status">
                        <?php
                        switch ($match['status']) {
                            case 'scheduled':
                                echo '<span class="status-badge scheduled">مجدولة</span>';
                                break;
                            case 'live':
                                echo '<span class="status-badge live">مباشرة</span>';
                                break;
                            case 'finished':
                                echo '<span class="status-badge finished">انتهت</span>';
                                break;
                        }
                        ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav class="pagination-nav" aria-label="تصفح المباريات">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page -->
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?status=<?php echo urlencode($status); ?>&league=<?php echo urlencode($league); ?>&date=<?php echo urlencode($date); ?>&page=<?php echo $page - 1; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                    السابق
                                </a>
                            </li>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php
                        $start = max(1, $page - 2);
                        $end = min($totalPages, $page + 2);
                        
                        for ($i = $start; $i <= $end; $i++) {
                            $active = $i == $page ? 'active' : '';
                            echo '<li class="page-item ' . $active . '">
                                    <a class="page-link" href="?status=' . urlencode($status) . '&league=' . urlencode($league) . '&date=' . urlencode($date) . '&page=' . $i . '">' . $i . '</a>
                                  </li>';
                        }
                        ?>

                        <!-- Next Page -->
                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?status=<?php echo urlencode($status); ?>&league=<?php echo urlencode($league); ?>&date=<?php echo urlencode($date); ?>&page=<?php echo $page + 1; ?>">
                                    التالي
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php else: ?>
            <!-- No Matches -->
            <div class="no-matches">
                <div class="no-matches-icon">
                    <i class="fas fa-futbol"></i>
                </div>
                <h3>لا توجد مباريات</h3>
                <p>لم يتم العثور على مباريات تطابق المعايير المحددة</p>
                <a href="<?php echo SITE_URL; ?>/matches.php" class="btn btn-primary">
                    <i class="fas fa-refresh"></i>
                    عرض جميع المباريات
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-3">
        <!-- Live Matches -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-broadcast-tower"></i>
                المباريات المباشرة
            </div>
            <div class="widget-content">
                <?php
                try {
                    $stmt = $pdo->prepare("SELECT * FROM matches WHERE status = 'live' ORDER BY match_date ASC LIMIT 5");
                    $stmt->execute();
                    $liveMatches = $stmt->fetchAll();
                    
                    if (!empty($liveMatches)) {
                        foreach ($liveMatches as $liveMatch) {
                            echo '<div class="live-match-item">
                                    <div class="live-indicator">
                                        <i class="fas fa-circle"></i>
                                        مباشر
                                    </div>
                                    <div class="live-teams">
                                        <div class="live-team">' . htmlspecialchars($liveMatch['home_team']) . '</div>
                                        <div class="live-score">' . ($liveMatch['home_score'] ?? 0) . ' - ' . ($liveMatch['away_score'] ?? 0) . '</div>
                                        <div class="live-team">' . htmlspecialchars($liveMatch['away_team']) . '</div>
                                    </div>
                                    <div class="live-league">' . htmlspecialchars($liveMatch['league']) . '</div>
                                  </div>';
                        }
                    } else {
                        echo '<p class="no-live-matches">لا توجد مباريات مباشرة حالياً</p>';
                    }
                } catch(PDOException $e) {
                    echo '<p class="no-live-matches">حدث خطأ في تحميل المباريات المباشرة</p>';
                }
                ?>
            </div>
        </div>

        <!-- Today's Matches -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-calendar-day"></i>
                مباريات اليوم
            </div>
            <div class="widget-content">
                <?php
                try {
                    $stmt = $pdo->prepare("SELECT * FROM matches WHERE DATE(match_date) = CURDATE() ORDER BY match_date ASC LIMIT 5");
                    $stmt->execute();
                    $todayMatches = $stmt->fetchAll();
                    
                    if (!empty($todayMatches)) {
                        foreach ($todayMatches as $todayMatch) {
                            echo '<div class="today-match-item">
                                    <div class="today-time">' . date('H:i', strtotime($todayMatch['match_date'])) . '</div>
                                    <div class="today-teams">
                                        <div>' . htmlspecialchars($todayMatch['home_team']) . ' vs ' . htmlspecialchars($todayMatch['away_team']) . '</div>
                                    </div>
                                    <div class="today-league">' . htmlspecialchars($todayMatch['league']) . '</div>
                                  </div>';
                        }
                    } else {
                        echo '<p class="no-today-matches">لا توجد مباريات اليوم</p>';
                    }
                } catch(PDOException $e) {
                    echo '<p class="no-today-matches">حدث خطأ في تحميل مباريات اليوم</p>';
                }
                ?>
            </div>
        </div>

        <!-- Leagues -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-trophy"></i>
                البطولات
            </div>
            <div class="widget-content">
                <ul class="leagues-list">
                    <?php foreach ($leagues as $leagueItem): ?>
                        <li>
                            <a href="?league=<?php echo urlencode($leagueItem); ?>">
                                <?php echo htmlspecialchars($leagueItem); ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.matches-header {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 25px;
    text-align: center;
}

.matches-title {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.matches-subtitle {
    color: var(--text-light);
    margin: 0;
}

.matches-filters {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 25px;
}

.date-separator {
    margin: 30px 0 20px 0;
}

.match-date {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 600;
    padding: 10px 0;
    border-bottom: 2px solid var(--primary-color);
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.match-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 15px;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 20px;
    align-items: center;
    transition: var(--transition);
}

.match-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.match-card.live {
    border-right: 4px solid #ef4444;
}

.match-card.finished {
    border-right: 4px solid var(--accent-color);
}

.match-card.scheduled {
    border-right: 4px solid var(--primary-color);
}

.match-info {
    text-align: center;
}

.match-league {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

.match-time {
    color: var(--text-light);
    font-size: 0.9rem;
}

.live-indicator {
    color: #ef4444;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.match-teams {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 15px;
    align-items: center;
    text-align: center;
}

.team-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 1.1rem;
}

.team-score {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-top: 5px;
}

.match-vs {
    display: flex;
    align-items: center;
    justify-content: center;
}

.vs-text {
    color: var(--text-light);
    font-weight: 600;
    font-size: 0.9rem;
}

.score-separator {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.5rem;
}

.match-status {
    text-align: center;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.scheduled {
    background: var(--primary-color);
    color: white;
}

.status-badge.live {
    background: #ef4444;
    color: white;
}

.status-badge.finished {
    background: var(--accent-color);
    color: white;
}

.no-matches {
    text-align: center;
    padding: 60px 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.no-matches-icon {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.live-match-item,
.today-match-item {
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.live-match-item:last-child,
.today-match-item:last-child {
    border-bottom: none;
}

.live-teams,
.today-teams {
    margin: 8px 0;
    font-weight: 600;
    color: var(--text-color);
}

.live-score {
    color: var(--primary-color);
    font-weight: 700;
    margin: 5px 0;
}

.live-league,
.today-league {
    font-size: 0.8rem;
    color: var(--text-light);
}

.today-time {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.leagues-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.leagues-list li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.leagues-list li:last-child {
    border-bottom: none;
}

.leagues-list a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.leagues-list a:hover {
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .match-card {
        grid-template-columns: 1fr;
        gap: 15px;
        text-align: center;
    }
    
    .matches-title {
        font-size: 2rem;
    }
    
    .filters-form .row {
        gap: 10px;
    }
    
    .filters-form .col-md-3 {
        margin-bottom: 10px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
