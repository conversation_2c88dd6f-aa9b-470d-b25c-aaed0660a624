<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم - موقع الأخبار العربي</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .test-section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #dc2626;
            font-weight: 700;
            margin-bottom: 20px;
            border-bottom: 2px solid #dc2626;
            padding-bottom: 10px;
        }
        
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .test-pass {
            border-color: #059669;
            background: #f0fdf4;
        }
        
        .test-fail {
            border-color: #dc2626;
            background: #fef2f2;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-5" style="color: #dc2626;">اختبار شامل لتصميم الموقع</h1>
        
        <!-- Breaking News Ticker Test -->
        <div class="test-section">
            <h2 class="test-title">اختبار شريط الأخبار العاجلة</h2>
            <div class="breaking-news-ticker">
                <div class="container-fluid">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <span class="breaking-label">عاجل</span>
                        </div>
                        <div class="col">
                            <div class="ticker-content">
                                <div class="ticker-text">
                                    <a href="#">خبر عاجل أول</a>
                                    <a href="#">خبر عاجل ثاني</a>
                                    <a href="#">خبر عاجل ثالث</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Header Test -->
        <div class="test-section">
            <h2 class="test-title">اختبار الهيدر والتنقل</h2>
            <header class="main-header">
                <div class="top-bar">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="date-time">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>الأحد، 15 ديسمبر 2024</span>
                                    <span class="mx-2">|</span>
                                    <i class="fas fa-clock"></i>
                                    <span>14:30</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="social-links text-end">
                                    <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                                    <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                                    <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                                    <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="header-main">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="logo">
                                    <h1 class="logo-text">موقع الأخبار</h1>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="search-box">
                                    <form class="search-form">
                                        <div class="input-group">
                                            <input type="text" class="form-control search-input" placeholder="ابحث في الأخبار...">
                                            <button class="btn btn-search" type="submit">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <nav class="main-navigation">
                    <div class="container">
                        <div class="navbar navbar-expand-lg">
                            <div class="collapse navbar-collapse">
                                <ul class="navbar-nav me-auto">
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#"><i class="fas fa-home"></i> الرئيسية</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">السياسة</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">الرياضة</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">التكنولوجيا</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>
        </div>
        
        <!-- Article Cards Test -->
        <div class="test-section">
            <h2 class="test-title">اختبار بطاقات المقالات</h2>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="article-card">
                        <img src="assets/images/placeholder.jpg" alt="مقال تجريبي" class="article-image">
                        <div class="article-content">
                            <div class="article-category-wrapper mb-2">
                                <a href="#" class="article-category">السياسة</a>
                            </div>
                            <h4 class="article-title">
                                <a href="#">عنوان المقال التجريبي الأول</a>
                            </h4>
                            <p class="article-excerpt">
                                هذا نص تجريبي لمحتوى المقال. يجب أن يظهر بشكل واضح ومقروء مع التنسيق المناسب.
                            </p>
                            <div class="article-meta">
                                <div class="meta-left">
                                    <span><i class="fas fa-user"></i> الكاتب</span>
                                    <span><i class="fas fa-clock"></i> منذ ساعة</span>
                                </div>
                                <div class="meta-right">
                                    <span><i class="fas fa-eye"></i> 1,234</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="article-card">
                        <img src="assets/images/placeholder.jpg" alt="مقال تجريبي" class="article-image">
                        <div class="article-content">
                            <div class="article-category-wrapper mb-2">
                                <a href="#" class="article-category">الرياضة</a>
                            </div>
                            <h4 class="article-title">
                                <a href="#">عنوان المقال التجريبي الثاني</a>
                            </h4>
                            <p class="article-excerpt">
                                هذا نص تجريبي آخر لمحتوى المقال. يجب أن يظهر بشكل واضح ومقروء مع التنسيق المناسب.
                            </p>
                            <div class="article-meta">
                                <div class="meta-left">
                                    <span><i class="fas fa-user"></i> الكاتب</span>
                                    <span><i class="fas fa-clock"></i> منذ ساعتين</span>
                                </div>
                                <div class="meta-right">
                                    <span><i class="fas fa-eye"></i> 2,567</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Buttons Test -->
        <div class="test-section">
            <h2 class="test-title">اختبار الأزرار</h2>
            <div class="d-flex flex-wrap gap-3">
                <button class="btn btn-primary">زر أساسي</button>
                <button class="btn btn-outline-primary">زر ثانوي</button>
                <button class="load-more-btn">تحميل المزيد</button>
                <button class="share-btn btn-facebook">فيسبوك</button>
                <button class="share-btn btn-twitter">تويتر</button>
            </div>
        </div>
        
        <!-- Responsive Test -->
        <div class="test-section">
            <h2 class="test-title">اختبار التجاوب</h2>
            <div class="test-item">
                <p><strong>تعليمات الاختبار:</strong></p>
                <ul>
                    <li>قم بتغيير حجم النافذة لاختبار التجاوب</li>
                    <li>تأكد من أن جميع العناصر تظهر بشكل صحيح على الهاتف المحمول</li>
                    <li>اختبر التنقل على الشاشات الصغيرة</li>
                    <li>تأكد من وضوح النصوص على جميع الأحجام</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Test JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Design test page loaded successfully');
            
            // Test toast notification
            setTimeout(() => {
                showToast('اختبار الإشعارات يعمل بنجاح!', 'success');
            }, 2000);
        });
        
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
