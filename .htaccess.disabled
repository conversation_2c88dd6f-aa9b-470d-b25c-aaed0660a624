# Arabic News Website - Apache Configuration

# Enable URL Rewriting
RewriteEngine On

# Basic Security Headers (simplified)
<IfModule mod_headers.c>
    Header always append X-Frame-Options SAMEORIGIN
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options nosniff
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # Default
    ExpiresDefault "access plus 1 week"
</IfModule>

# Security - Hide sensitive files
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to version control files
<FilesMatch "\.(git|svn)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# URL Rewriting Rules
RewriteEngine On
RewriteBase /

# Remove trailing slash from URLs (except directories)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{THE_REQUEST} /+[^?\s]*?/[\s?] [NC]
RewriteRule ^(.+)/$ /$1 [R=301,L]

# Force HTTPS (uncomment in production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Article URLs: /article/slug -> article.php?slug=slug
RewriteRule ^article/([a-zA-Z0-9\-\u0600-\u06FF]+)/?$ article.php?slug=$1 [L,QSA]

# Category URLs: /category/slug -> category.php?slug=slug
RewriteRule ^category/([a-zA-Z0-9\-\u0600-\u06FF]+)/?$ category.php?slug=$1 [L,QSA]

# Tag URLs: /tag/slug -> tag.php?slug=slug
RewriteRule ^tag/([a-zA-Z0-9\-\u0600-\u06FF]+)/?$ tag.php?slug=$1 [L,QSA]

# Author URLs: /author/username -> author.php?username=username
RewriteRule ^author/([a-zA-Z0-9\-_]+)/?$ author.php?username=$1 [L,QSA]

# Search URLs: /search/query -> search.php?q=query
RewriteRule ^search/([^/]+)/?$ search.php?q=$1 [L,QSA]

# Archive URLs: /archive/year/month -> archive.php?year=year&month=month
RewriteRule ^archive/([0-9]{4})/([0-9]{1,2})/?$ archive.php?year=$1&month=$2 [L,QSA]
RewriteRule ^archive/([0-9]{4})/?$ archive.php?year=$1 [L,QSA]

# Page URLs: /page/slug -> page.php?slug=slug
RewriteRule ^page/([a-zA-Z0-9\-\u0600-\u06FF]+)/?$ page.php?slug=$1 [L,QSA]

# RSS Feed: /rss -> rss.php
RewriteRule ^rss/?$ rss.php [L]

# Sitemap: /sitemap.xml -> sitemap.php
RewriteRule ^sitemap\.xml$ sitemap.php [L]

# Admin panel protection (optional - add IP restrictions)
# <Directory "admin">
#     Order Deny,Allow
#     Deny from all
#     Allow from 127.0.0.1
#     Allow from YOUR_IP_ADDRESS
# </Directory>

# Prevent hotlinking of images
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?google.com [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?facebook.com [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [F]

# Error Pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Charset
AddDefaultCharset UTF-8

# MIME Types
<IfModule mod_mime.c>
    # Web fonts
    AddType application/font-woff woff
    AddType application/font-woff2 woff2
    AddType application/vnd.ms-fontobject eot
    AddType font/truetype ttf
    AddType font/opentype otf
    
    # SVG
    AddType image/svg+xml svg svgz
    AddEncoding gzip svgz
    
    # Web app manifest
    AddType application/manifest+json webmanifest
    
    # JSON
    AddType application/json json
    
    # WebP
    AddType image/webp webp
</IfModule>

# Prevent access to PHP files in uploads directory
<Directory "uploads">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.phtml">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.php3">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.php4">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.php5">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.pl">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.py">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.jsp">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.asp">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.sh">
        Order Allow,Deny
        Deny from all
    </Files>
    <Files "*.cgi">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

# Limit file upload size (adjust as needed)
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# Hide PHP version
<IfModule mod_headers.c>
    Header unset X-Powered-By
    Header unset Server
</IfModule>

# Prevent access to includes directory
<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

# Prevent access to config directory
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

# Custom 404 handling for missing images
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} \.(jpg|jpeg|png|gif|webp)$ [NC]
RewriteRule . /assets/images/placeholder.jpg [L]
