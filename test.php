<?php
// Simple test file to check if PHP is working
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP Test Page</h1>";
echo "<p>✅ PHP is working!</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test database connection
echo "<h2>Database Test</h2>";
try {
    // Test basic PDO connection
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p>✅ MySQL connection successful</p>";
    
    // Test if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'arabic_news'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ Database 'arabic_news' exists</p>";
    } else {
        echo "<p>⚠️ Database 'arabic_news' does not exist</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>File System Test</h2>";
if (is_writable('.')) {
    echo "<p>✅ Current directory is writable</p>";
} else {
    echo "<p>❌ Current directory is not writable</p>";
}

if (file_exists('config/config.php')) {
    echo "<p>✅ config.php exists</p>";
} else {
    echo "<p>❌ config.php missing</p>";
}

phpinfo();
?>
