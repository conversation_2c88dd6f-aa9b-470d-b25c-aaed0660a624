<?php
require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirectTo(SITE_URL . '/admin/');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']);
    
    if (!empty($username) && !empty($password)) {
        try {
            $stmt = $pdo->prepare("SELECT id, username, email, password, role FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Login user using SessionManager
                SessionManager::login($user['id'], $user['username'], $user['role']);
                
                // Set remember me cookie if requested
                if ($remember) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                    
                    // Store token in database (you might want to create a remember_tokens table)
                    $stmt = $pdo->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                    $stmt->execute([$token, $user['id']]);
                }
                
                // Redirect to admin panel or requested page
                $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '/admin/';
                redirectTo(SITE_URL . $redirect);
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch(PDOException $e) {
            $error = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    } else {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    }
}

$pageTitle = 'تسجيل الدخول';
include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="login-container">
            <div class="login-header">
                <h2 class="login-title">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </h2>
                <p class="login-subtitle">أدخل بياناتك للوصول إلى لوحة التحكم</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <form method="POST" class="login-form" data-validate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user"></i>
                        اسم المستخدم أو البريد الإلكتروني
                    </label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           class="form-control" 
                           placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                           value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                           required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               class="form-control" 
                               placeholder="أدخل كلمة المرور"
                               required>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" 
                               id="remember" 
                               name="remember" 
                               class="form-check-input"
                               <?php echo isset($_POST['remember']) ? 'checked' : ''; ?>>
                        <label for="remember" class="form-check-label">
                            تذكرني لمدة 30 يوماً
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div class="login-footer">
                <div class="forgot-password">
                    <a href="<?php echo SITE_URL; ?>/forgot-password.php">
                        <i class="fas fa-key"></i>
                        نسيت كلمة المرور؟
                    </a>
                </div>
                
                <div class="back-to-site">
                    <a href="<?php echo SITE_URL; ?>/">
                        <i class="fas fa-arrow-right"></i>
                        العودة إلى الموقع
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.login-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 40px;
    margin: 50px 0;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-title {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.login-subtitle {
    color: var(--text-light);
    margin: 0;
}

.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-control {
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--primary-color);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-login {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.forgot-password {
    margin-bottom: 15px;
}

.forgot-password a,
.back-to-site a {
    color: var(--primary-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.forgot-password a:hover,
.back-to-site a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

@media (max-width: 576px) {
    .login-container {
        padding: 30px 20px;
        margin: 20px 0;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
}
</style>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Auto-focus on username field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('username').focus();
});
</script>

<?php include 'includes/footer.php'; ?>
