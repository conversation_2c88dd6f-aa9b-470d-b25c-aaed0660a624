<?php
/**
 * Newsletter Subscription API Endpoint
 * Handles newsletter subscription requests
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed',
        'message' => 'يُسمح فقط بطلبات POST'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Fallback to $_POST if JSON parsing fails
    if (!$input) {
        $input = $_POST;
    }

    $email = isset($input['email']) ? trim($input['email']) : '';
    $name = isset($input['name']) ? trim($input['name']) : '';
    $preferences = isset($input['preferences']) ? $input['preferences'] : [];

    // Validate email
    if (empty($email)) {
        throw new Exception('البريد الإلكتروني مطلوب');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('البريد الإلكتروني غير صحيح');
    }

    // Check if email already exists
    $checkQuery = "SELECT id, status, created_at FROM newsletter_subscribers WHERE email = ?";
    $checkStmt = $pdo->prepare($checkQuery);
    $checkStmt->execute([$email]);
    $existingSubscriber = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if ($existingSubscriber) {
        if ($existingSubscriber['status'] === 'active') {
            echo json_encode([
                'success' => false,
                'error' => 'already_subscribed',
                'message' => 'هذا البريد الإلكتروني مشترك بالفعل في النشرة الإخبارية'
            ], JSON_UNESCAPED_UNICODE);
            exit();
        } else {
            // Reactivate subscription
            $updateQuery = "UPDATE newsletter_subscribers SET status = 'active', updated_at = NOW() WHERE email = ?";
            $updateStmt = $pdo->prepare($updateQuery);
            $updateStmt->execute([$email]);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تفعيل اشتراكك في النشرة الإخبارية مرة أخرى',
                'data' => [
                    'email' => $email,
                    'status' => 'reactivated'
                ]
            ], JSON_UNESCAPED_UNICODE);
            exit();
        }
    }

    // Generate verification token
    $verificationToken = bin2hex(random_bytes(32));
    
    // Insert new subscriber
    $insertQuery = "
        INSERT INTO newsletter_subscribers 
        (email, name, verification_token, status, preferences, ip_address, user_agent, created_at, updated_at) 
        VALUES (?, ?, ?, 'pending', ?, ?, ?, NOW(), NOW())
    ";
    
    $insertStmt = $pdo->prepare($insertQuery);
    $insertStmt->execute([
        $email,
        $name,
        $verificationToken,
        json_encode($preferences),
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);

    $subscriberId = $pdo->lastInsertId();

    // Send verification email (optional - can be implemented later)
    $verificationLink = SITE_URL . "/verify-newsletter.php?token=" . $verificationToken;
    
    // For now, we'll auto-activate the subscription
    // In production, you would send an email and require verification
    $activateQuery = "UPDATE newsletter_subscribers SET status = 'active' WHERE id = ?";
    $activateStmt = $pdo->prepare($activateQuery);
    $activateStmt->execute([$subscriberId]);

    // Log subscription for analytics
    try {
        $logQuery = "INSERT INTO newsletter_logs (subscriber_id, action, ip_address, user_agent, created_at) VALUES (?, 'subscribe', ?, ?, NOW())";
        $logStmt = $pdo->prepare($logQuery);
        $logStmt->execute([
            $subscriberId,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        // Log error but don't fail the subscription
        error_log("Newsletter logging failed: " . $e->getMessage());
    }

    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'تم الاشتراك في النشرة الإخبارية بنجاح! شكراً لك',
        'data' => [
            'email' => $email,
            'subscriber_id' => $subscriberId,
            'status' => 'active',
            'verification_required' => false
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'subscription_failed',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// Create newsletter_subscribers table if it doesn't exist
function createNewsletterTable($pdo) {
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS newsletter_subscribers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            name VARCHAR(255),
            verification_token VARCHAR(64),
            status ENUM('pending', 'active', 'unsubscribed') DEFAULT 'pending',
            preferences JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            verified_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTableQuery);
}

// Create newsletter_logs table if it doesn't exist
function createNewsletterLogsTable($pdo) {
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS newsletter_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            subscriber_id INT,
            action ENUM('subscribe', 'unsubscribe', 'verify', 'send') NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (subscriber_id) REFERENCES newsletter_subscribers(id) ON DELETE CASCADE,
            INDEX idx_subscriber_id (subscriber_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTableQuery);
}

// Create search_logs table if it doesn't exist
function createSearchLogsTable($pdo) {
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS search_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            keywords VARCHAR(255),
            category VARCHAR(100),
            author VARCHAR(100),
            date_from DATE,
            date_to DATE,
            results_count INT DEFAULT 0,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_keywords (keywords),
            INDEX idx_category (category),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTableQuery);
}

// Initialize tables
try {
    createNewsletterTable($pdo);
    createNewsletterLogsTable($pdo);
    createSearchLogsTable($pdo);
} catch (Exception $e) {
    error_log("Table creation failed: " . $e->getMessage());
}
?>
