<?php
// Global configuration file

// Session management
require_once __DIR__ . '/session.php';

// Site configuration
define('SITE_URL', 'http://localhost/amr');
define('SITE_NAME', 'الموقع الإخباري العربي');
define('SITE_DESCRIPTION', 'موقع إخباري عربي شامل');

// Database configuration
require_once 'database.php';

// Timezone
date_default_timezone_set('Asia/Riyadh');

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// File upload settings
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Pagination settings
define('ARTICLES_PER_PAGE', 10);
define('COMMENTS_PER_PAGE', 20);

// RSS settings
define('RSS_UPDATE_INTERVAL', 30); // minutes

// Helper functions
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function generateSlug($text) {
    // Convert Arabic text to slug
    $text = trim($text);
    $text = preg_replace('/[^\p{Arabic}\p{L}\p{N}\s\-_]/u', '', $text);
    $text = preg_replace('/[\s\-_]+/', '-', $text);
    $text = trim($text, '-');
    return strtolower($text);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'منذ لحظات';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    if ($time < 31536000) return 'منذ ' . floor($time/2592000) . ' شهر';
    return 'منذ ' . floor($time/31536000) . ' سنة';
}

function isLoggedIn() {
    return SessionManager::isLoggedIn();
}

function isAdmin() {
    return SessionManager::isAdmin();
}

function isEditor() {
    return SessionManager::isEditor();
}

function redirectTo($url) {
    header("Location: $url");
    exit();
}

function showAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

function displayAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        echo "<div class='alert alert-{$alert['type']} alert-dismissible fade show' role='alert'>
                {$alert['message']}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
              </div>";
        unset($_SESSION['alert']);
    }
}

function uploadFile($file, $directory = 'images') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $uploadDir = UPLOAD_PATH . $directory . '/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, ALLOWED_EXTENSIONS)) {
        return false;
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    $filename = uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $directory . '/' . $filename;
    }
    
    return false;
}

function formatNumber($number) {
    if ($number >= 1000000) {
        return number_format($number / 1000000, 1) . 'م';
    } elseif ($number >= 1000) {
        return number_format($number / 1000, 1) . 'ك';
    }
    return number_format($number);
}

function truncateText($text, $length = 150) {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length) . '...';
}

// CSRF Protection
function generateCSRFToken() {
    return SessionManager::generateCSRFToken();
}

function verifyCSRFToken($token) {
    return SessionManager::verifyCSRFToken($token);
}

// Database connection
$database = new Database();
$pdo = $database->connect();

// Get site settings
function getSetting($key, $default = '') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : $default;
    } catch(PDOException $e) {
        return $default;
    }
}

function setSetting($key, $value) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                              ON DUPLICATE KEY UPDATE setting_value = ?");
        return $stmt->execute([$key, $value, $value]);
    } catch(PDOException $e) {
        return false;
    }
}
?>
