<?php
require_once '../config/config.php';

// RSS Feed Fetcher Class
class RSSFetcher {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function fetchAllFeeds() {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM rss_feeds WHERE is_active = 1");
            $stmt->execute();
            $feeds = $stmt->fetchAll();
            
            foreach ($feeds as $feed) {
                $this->fetchFeed($feed);
            }
            
            return true;
        } catch(PDOException $e) {
            error_log("RSS Fetch Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function fetchFeed($feed) {
        try {
            // Set user agent to avoid blocking
            $context = stream_context_create([
                'http' => [
                    'user_agent' => 'Mozilla/5.0 (compatible; Arabic News Bot)',
                    'timeout' => 30
                ]
            ]);
            
            // Fetch RSS content
            $rssContent = file_get_contents($feed['url'], false, $context);
            
            if ($rssContent === false) {
                error_log("Failed to fetch RSS feed: " . $feed['url']);
                return false;
            }
            
            // Parse XML
            $xml = simplexml_load_string($rssContent);
            
            if ($xml === false) {
                error_log("Failed to parse RSS XML: " . $feed['url']);
                return false;
            }
            
            // Process RSS items
            $this->processRSSItems($xml, $feed);
            
            // Update last fetched time
            $stmt = $this->pdo->prepare("UPDATE rss_feeds SET last_fetched = NOW() WHERE id = ?");
            $stmt->execute([$feed['id']]);
            
            return true;
            
        } catch(Exception $e) {
            error_log("RSS Processing Error: " . $e->getMessage());
            return false;
        }
    }
    
    private function processRSSItems($xml, $feed) {
        $items = [];
        
        // Handle different RSS formats
        if (isset($xml->channel->item)) {
            $items = $xml->channel->item;
        } elseif (isset($xml->item)) {
            $items = $xml->item;
        } elseif (isset($xml->entry)) {
            // Atom feed
            $items = $xml->entry;
        }
        
        foreach ($items as $item) {
            $this->processRSSItem($item, $feed);
        }
    }
    
    private function processRSSItem($item, $feed) {
        try {
            // Extract item data
            $title = $this->cleanText((string)$item->title);
            $description = $this->cleanText((string)($item->description ?? $item->summary ?? ''));
            $link = (string)($item->link ?? $item->id ?? '');
            $pubDate = (string)($item->pubDate ?? $item->published ?? $item->updated ?? '');
            
            // Skip if essential data is missing
            if (empty($title) || empty($link)) {
                return false;
            }
            
            // Generate slug
            $slug = $this->generateUniqueSlug($title);
            
            // Parse publication date
            $publishedAt = $this->parseDate($pubDate);
            
            // Check if article already exists
            $stmt = $this->pdo->prepare("SELECT id FROM articles WHERE slug = ?");
            $stmt->execute([$slug]);
            
            if ($stmt->fetch()) {
                return false; // Article already exists
            }
            
            // Extract and download featured image
            $featuredImage = $this->extractFeaturedImage($item, $description);
            
            // Create excerpt from description
            $excerpt = $this->createExcerpt($description);
            
            // Insert article
            $stmt = $this->pdo->prepare("
                INSERT INTO articles (
                    title, slug, content, excerpt, featured_image, 
                    category_id, author_id, status, created_at, published_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'published', NOW(), ?)
            ");
            
            $defaultAuthorId = 1; // Admin user
            $stmt->execute([
                $title,
                $slug,
                $description,
                $excerpt,
                $featuredImage,
                $feed['category_id'],
                $defaultAuthorId,
                $publishedAt
            ]);
            
            return true;
            
        } catch(PDOException $e) {
            error_log("RSS Item Processing Error: " . $e->getMessage());
            return false;
        }
    }
    
    private function cleanText($text) {
        // Remove HTML tags and clean text
        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        $text = trim($text);
        return $text;
    }
    
    private function generateUniqueSlug($title) {
        $baseSlug = generateSlug($title);
        $slug = $baseSlug;
        $counter = 1;
        
        while (true) {
            $stmt = $this->pdo->prepare("SELECT id FROM articles WHERE slug = ?");
            $stmt->execute([$slug]);
            
            if (!$stmt->fetch()) {
                break;
            }
            
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    private function parseDate($dateString) {
        if (empty($dateString)) {
            return date('Y-m-d H:i:s');
        }
        
        $timestamp = strtotime($dateString);
        if ($timestamp === false) {
            return date('Y-m-d H:i:s');
        }
        
        return date('Y-m-d H:i:s', $timestamp);
    }
    
    private function extractFeaturedImage($item, $description) {
        // Try to extract image from various RSS elements
        $imageUrl = '';
        
        // Check for media:content or media:thumbnail
        if (isset($item->children('media', true)->content)) {
            $imageUrl = (string)$item->children('media', true)->content->attributes()->url;
        } elseif (isset($item->children('media', true)->thumbnail)) {
            $imageUrl = (string)$item->children('media', true)->thumbnail->attributes()->url;
        }
        // Check for enclosure
        elseif (isset($item->enclosure) && strpos((string)$item->enclosure->attributes()->type, 'image') !== false) {
            $imageUrl = (string)$item->enclosure->attributes()->url;
        }
        // Extract from description
        else {
            preg_match('/<img[^>]+src="([^"]+)"/', $description, $matches);
            if (isset($matches[1])) {
                $imageUrl = $matches[1];
            }
        }
        
        // Download and save image
        if (!empty($imageUrl)) {
            return $this->downloadImage($imageUrl);
        }
        
        return null;
    }
    
    private function downloadImage($imageUrl) {
        try {
            // Validate URL
            if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                return null;
            }
            
            // Get image info
            $imageInfo = getimagesize($imageUrl);
            if ($imageInfo === false) {
                return null;
            }
            
            // Check if it's a valid image type
            $allowedTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF, IMAGETYPE_WEBP];
            if (!in_array($imageInfo[2], $allowedTypes)) {
                return null;
            }
            
            // Generate filename
            $extension = image_type_to_extension($imageInfo[2], false);
            $filename = 'rss_' . uniqid() . '.' . $extension;
            $uploadDir = UPLOAD_PATH . 'images/';
            
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            $filepath = $uploadDir . $filename;
            
            // Download image
            $context = stream_context_create([
                'http' => [
                    'user_agent' => 'Mozilla/5.0 (compatible; Arabic News Bot)',
                    'timeout' => 30
                ]
            ]);
            
            $imageData = file_get_contents($imageUrl, false, $context);
            
            if ($imageData !== false && file_put_contents($filepath, $imageData)) {
                return 'images/' . $filename;
            }
            
        } catch(Exception $e) {
            error_log("Image Download Error: " . $e->getMessage());
        }
        
        return null;
    }
    
    private function createExcerpt($content) {
        $excerpt = strip_tags($content);
        $excerpt = html_entity_decode($excerpt, ENT_QUOTES, 'UTF-8');
        return truncateText($excerpt, 200);
    }
}

// Auto-fetch RSS feeds if called directly
if (basename($_SERVER['PHP_SELF']) === 'fetch.php') {
    $fetcher = new RSSFetcher($pdo);
    
    if ($fetcher->fetchAllFeeds()) {
        echo "RSS feeds fetched successfully\n";
    } else {
        echo "Error fetching RSS feeds\n";
    }
}

// Function to be called by cron job
function fetchRSSFeeds() {
    global $pdo;
    $fetcher = new RSSFetcher($pdo);
    return $fetcher->fetchAllFeeds();
}
?>
