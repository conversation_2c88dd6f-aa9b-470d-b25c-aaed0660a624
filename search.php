<?php
require_once 'config/config.php';

// Get search query
$query = isset($_GET['q']) ? sanitize($_GET['q']) : '';
$category = isset($_GET['category']) ? sanitize($_GET['category']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ARTICLES_PER_PAGE;
$offset = ($page - 1) * $limit;

$articles = [];
$totalArticles = 0;
$totalPages = 0;

if (!empty($query)) {
    try {
        // Build search query
        $searchSQL = "
            SELECT a.*, c.name as category_name, c.slug as category_slug, u.username as author_name
            FROM articles a 
            LEFT JOIN categories c ON a.category_id = c.id 
            LEFT JOIN users u ON a.author_id = u.id 
            WHERE a.status = 'published' 
            AND (a.title LIKE ? OR a.content LIKE ? OR a.excerpt LIKE ?)
        ";
        
        $countSQL = "
            SELECT COUNT(*) as total
            FROM articles a 
            WHERE a.status = 'published' 
            AND (a.title LIKE ? OR a.content LIKE ? OR a.excerpt LIKE ?)
        ";
        
        $searchTerm = '%' . $query . '%';
        $params = [$searchTerm, $searchTerm, $searchTerm];
        
        // Add category filter if specified
        if (!empty($category)) {
            $searchSQL .= " AND c.slug = ?";
            $countSQL .= " AND a.category_id = (SELECT id FROM categories WHERE slug = ?)";
            $params[] = $category;
        }
        
        // Count total results
        $stmt = $pdo->prepare($countSQL);
        $stmt->execute($params);
        $totalArticles = $stmt->fetch()['total'];
        $totalPages = ceil($totalArticles / $limit);
        
        // Get search results
        $searchSQL .= " ORDER BY a.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($searchSQL);
        $stmt->execute($params);
        $articles = $stmt->fetchAll();
        
    } catch(PDOException $e) {
        $articles = [];
        $totalArticles = 0;
    }
}

// Get categories for filter
try {
    $stmt = $pdo->prepare("SELECT name, slug FROM categories ORDER BY name");
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch(PDOException $e) {
    $categories = [];
}

$pageTitle = !empty($query) ? 'نتائج البحث عن: ' . $query : 'البحث';
$pageDescription = !empty($query) ? "نتائج البحث عن \"$query\" في موقع " . SITE_NAME : 'ابحث في جميع مقالات الموقع';

include 'includes/header.php';
?>

<div class="row">
    <!-- Main Content -->
    <div class="col-lg-8">
        <!-- Search Header -->
        <div class="search-header">
            <h1 class="search-title">
                <i class="fas fa-search"></i>
                <?php if (!empty($query)): ?>
                    نتائج البحث عن: "<?php echo htmlspecialchars($query); ?>"
                <?php else: ?>
                    البحث في الموقع
                <?php endif; ?>
            </h1>
            
            <?php if (!empty($query)): ?>
                <div class="search-meta">
                    <span class="results-count">
                        تم العثور على <?php echo formatNumber($totalArticles); ?> نتيجة
                    </span>
                    <?php if (!empty($category)): ?>
                        <span class="search-filter">
                            في قسم: <?php echo htmlspecialchars($category); ?>
                            <a href="?q=<?php echo urlencode($query); ?>" class="remove-filter">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Advanced Search Form -->
        <div class="search-form-container">
            <form method="GET" class="advanced-search-form">
                <div class="row">
                    <div class="col-md-8">
                        <div class="search-input-group">
                            <input type="text" 
                                   name="q" 
                                   class="form-control search-input" 
                                   placeholder="ابحث في العناوين والمحتوى..."
                                   value="<?php echo htmlspecialchars($query); ?>">
                            <button type="submit" class="btn btn-primary search-btn">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select name="category" class="form-select category-filter">
                            <option value="">جميع الأقسام</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['slug']; ?>" 
                                        <?php echo $category === $cat['slug'] ? 'selected' : ''; ?>>
                                    <?php echo $cat['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search Results -->
        <?php if (!empty($query)): ?>
            <?php if (!empty($articles)): ?>
                <div class="search-results">
                    <?php foreach ($articles as $article): ?>
                        <div class="search-result-item">
                            <div class="result-image">
                                <img src="<?php echo $article['featured_image'] ? SITE_URL . '/uploads/' . $article['featured_image'] : SITE_URL . '/assets/images/placeholder.jpg'; ?>" 
                                     alt="<?php echo htmlspecialchars($article['title']); ?>" 
                                     class="result-thumbnail">
                            </div>
                            
                            <div class="result-content">
                                <div class="result-category">
                                    <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $article['category_slug']; ?>" 
                                       class="category-link">
                                        <?php echo $article['category_name']; ?>
                                    </a>
                                </div>
                                
                                <h3 class="result-title">
                                    <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                        <?php 
                                        // Highlight search terms in title
                                        $highlightedTitle = str_ireplace($query, '<mark>' . $query . '</mark>', htmlspecialchars($article['title']));
                                        echo $highlightedTitle;
                                        ?>
                                    </a>
                                </h3>
                                
                                <p class="result-excerpt">
                                    <?php 
                                    $excerpt = $article['excerpt'] ?: strip_tags($article['content']);
                                    $excerpt = truncateText($excerpt, 200);
                                    // Highlight search terms in excerpt
                                    $highlightedExcerpt = str_ireplace($query, '<mark>' . $query . '</mark>', htmlspecialchars($excerpt));
                                    echo $highlightedExcerpt;
                                    ?>
                                </p>
                                
                                <div class="result-meta">
                                    <span class="author">
                                        <i class="fas fa-user"></i>
                                        <?php echo $article['author_name']; ?>
                                    </span>
                                    <span class="date">
                                        <i class="fas fa-clock"></i>
                                        <?php echo timeAgo($article['created_at']); ?>
                                    </span>
                                    <span class="views">
                                        <i class="fas fa-eye"></i>
                                        <?php echo formatNumber($article['views']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav class="pagination-nav" aria-label="تصفح نتائج البحث">
                        <ul class="pagination justify-content-center">
                            <!-- Previous Page -->
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?q=<?php echo urlencode($query); ?>&category=<?php echo urlencode($category); ?>&page=<?php echo $page - 1; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                        السابق
                                    </a>
                                </li>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $start = max(1, $page - 2);
                            $end = min($totalPages, $page + 2);
                            
                            for ($i = $start; $i <= $end; $i++) {
                                $active = $i == $page ? 'active' : '';
                                echo '<li class="page-item ' . $active . '">
                                        <a class="page-link" href="?q=' . urlencode($query) . '&category=' . urlencode($category) . '&page=' . $i . '">' . $i . '</a>
                                      </li>';
                            }
                            ?>

                            <!-- Next Page -->
                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?q=<?php echo urlencode($query); ?>&category=<?php echo urlencode($category); ?>&page=<?php echo $page + 1; ?>">
                                        التالي
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <!-- No Results -->
                <div class="no-results">
                    <div class="no-results-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>لم يتم العثور على نتائج</h3>
                    <p>لم نتمكن من العثور على أي مقالات تحتوي على "<?php echo htmlspecialchars($query); ?>"</p>
                    
                    <div class="search-suggestions">
                        <h5>اقتراحات للبحث:</h5>
                        <ul>
                            <li>تأكد من صحة الكلمات المكتوبة</li>
                            <li>جرب كلمات مفتاحية أخرى</li>
                            <li>استخدم كلمات أكثر عمومية</li>
                            <li>قم بإزالة فلتر القسم</li>
                        </ul>
                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- Search Tips -->
            <div class="search-tips">
                <h3>نصائح للبحث</h3>
                <div class="tips-grid">
                    <div class="tip-item">
                        <i class="fas fa-lightbulb"></i>
                        <h5>استخدم كلمات مفتاحية واضحة</h5>
                        <p>اكتب الكلمات الأساسية التي تريد البحث عنها</p>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-filter"></i>
                        <h5>استخدم فلتر الأقسام</h5>
                        <p>حدد قسماً معيناً لتضييق نطاق البحث</p>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-quote-left"></i>
                        <h5>ابحث عن عبارات محددة</h5>
                        <p>ضع العبارة بين علامتي تنصيص للبحث الدقيق</p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Popular Searches -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-fire"></i>
                عمليات بحث شائعة
            </div>
            <div class="widget-content">
                <div class="popular-searches">
                    <a href="?q=كرة القدم" class="search-tag">كرة القدم</a>
                    <a href="?q=تكنولوجيا" class="search-tag">تكنولوجيا</a>
                    <a href="?q=اقتصاد" class="search-tag">اقتصاد</a>
                    <a href="?q=سياسة" class="search-tag">سياسة</a>
                    <a href="?q=صحة" class="search-tag">صحة</a>
                    <a href="?q=تعليم" class="search-tag">تعليم</a>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-list"></i>
                تصفح حسب القسم
            </div>
            <div class="widget-content">
                <ul class="categories-list">
                    <?php foreach ($categories as $cat): ?>
                        <li>
                            <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $cat['slug']; ?>">
                                <?php echo $cat['name']; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.search-header {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 25px;
}

.search-title {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.search-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    color: var(--text-light);
}

.search-filter {
    background: var(--light-color);
    padding: 5px 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.remove-filter {
    color: var(--text-light);
    text-decoration: none;
    padding: 2px;
}

.remove-filter:hover {
    color: var(--secondary-color);
}

.search-form-container {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 25px;
}

.search-input-group {
    display: flex;
    gap: 0;
}

.search-input {
    border-left: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.search-btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 12px 20px;
}

.category-filter {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
}

.search-result-item {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    transition: var(--transition);
}

.search-result-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.result-image {
    flex-shrink: 0;
}

.result-thumbnail {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.result-content {
    flex: 1;
}

.result-category {
    margin-bottom: 8px;
}

.category-link {
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.8rem;
    transition: var(--transition);
}

.category-link:hover {
    background: #1d4ed8;
    color: white;
}

.result-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.4;
}

.result-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.result-title a:hover {
    color: var(--primary-color);
}

.result-excerpt {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 15px;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.result-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

mark {
    background: #fef3c7;
    color: #92400e;
    padding: 2px 4px;
    border-radius: 3px;
}

.no-results {
    text-align: center;
    padding: 60px 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.no-results-icon {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.search-suggestions {
    text-align: right;
    margin-top: 30px;
    padding: 20px;
    background: var(--light-color);
    border-radius: var(--border-radius);
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 15px 0 0 0;
}

.search-suggestions li {
    padding: 5px 0;
    color: var(--text-light);
}

.search-tips {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.tips-grid {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.tip-item {
    text-align: center;
    padding: 20px;
    background: var(--light-color);
    border-radius: var(--border-radius);
}

.tip-item i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.tip-item h5 {
    color: var(--text-color);
    margin-bottom: 10px;
}

.tip-item p {
    color: var(--text-light);
    margin: 0;
}

.popular-searches {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.search-tag {
    background: var(--light-color);
    color: var(--text-color);
    padding: 6px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.search-tag:hover {
    background: var(--primary-color);
    color: white;
}

.categories-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.categories-list li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.categories-list li:last-child {
    border-bottom: none;
}

.categories-list a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.categories-list a:hover {
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .search-result-item {
        flex-direction: column;
        gap: 15px;
    }
    
    .result-thumbnail {
        width: 100%;
        height: 200px;
    }
    
    .search-title {
        font-size: 1.5rem;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
