<?php
require_once 'config/config.php';

$pageTitle = 'الرئيسية';
$pageDescription = 'موقع إخباري عربي شامل - آخر الأخبار والمستجدات';

// Get featured article
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.slug as category_slug, u.username as author_name
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN users u ON a.author_id = u.id 
        WHERE a.status = 'published' 
        ORDER BY a.created_at DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $featuredArticle = $stmt->fetch();
} catch(PDOException $e) {
    $featuredArticle = null;
}

// Get latest articles
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.slug as category_slug, u.username as author_name
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN users u ON a.author_id = u.id 
        WHERE a.status = 'published' 
        ORDER BY a.created_at DESC 
        LIMIT 12
    ");
    $stmt->execute();
    $latestArticles = $stmt->fetchAll();
} catch(PDOException $e) {
    $latestArticles = [];
}

// Get popular articles
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.slug as category_slug, u.username as author_name
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN users u ON a.author_id = u.id 
        WHERE a.status = 'published' 
        ORDER BY a.views DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $popularArticles = $stmt->fetchAll();
} catch(PDOException $e) {
    $popularArticles = [];
}

// Get upcoming matches
try {
    $stmt = $pdo->prepare("
        SELECT * FROM matches 
        WHERE match_date >= NOW() 
        ORDER BY match_date ASC 
        LIMIT 5
    ");
    $stmt->execute();
    $upcomingMatches = $stmt->fetchAll();
} catch(PDOException $e) {
    $upcomingMatches = [];
}

// Get categories with article count
try {
    $stmt = $pdo->prepare("
        SELECT c.*, COALESCE(COUNT(a.id), 0) as article_count
        FROM categories c
        LEFT JOIN articles a ON c.id = a.category_id AND a.status = 'published'
        GROUP BY c.id
        ORDER BY c.name
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll();

    // Ensure article_count exists for each category
    foreach ($categories as &$category) {
        if (!isset($category['article_count'])) {
            $category['article_count'] = 0;
        }
    }
    unset($category); // Break the reference
} catch(PDOException $e) {
    $categories = [];
}

include 'includes/header.php';
?>

<div class="row">
    <!-- Main Content -->
    <div class="col-lg-8">
        <!-- Featured Article -->
        <?php if ($featuredArticle): ?>
        <div class="featured-article mb-4">
            <img src="<?php echo $featuredArticle['featured_image'] ? SITE_URL . '/uploads/' . $featuredArticle['featured_image'] : SITE_URL . '/assets/images/placeholder.jpg'; ?>" 
                 alt="<?php echo htmlspecialchars($featuredArticle['title']); ?>" 
                 class="article-image">
            <div class="featured-overlay">
                <div class="featured-category">
                    <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $featuredArticle['category_slug']; ?>" 
                       class="article-category">
                        <?php echo $featuredArticle['category_name']; ?>
                    </a>
                </div>
                <h2 class="featured-title">
                    <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $featuredArticle['slug']; ?>">
                        <?php echo htmlspecialchars($featuredArticle['title']); ?>
                    </a>
                </h2>
                <div class="featured-meta">
                    <span><i class="fas fa-user"></i> <?php echo $featuredArticle['author_name']; ?></span>
                    <span><i class="fas fa-clock"></i> <?php echo timeAgo($featuredArticle['created_at']); ?></span>
                    <span><i class="fas fa-eye"></i> <?php echo formatNumber($featuredArticle['views']); ?></span>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Latest Articles Section -->
        <div class="section-header mb-4">
            <h3 class="section-title">
                <i class="fas fa-newspaper"></i>
                آخر الأخبار
            </h3>
        </div>

        <div class="row" id="articlesContainer">
            <?php foreach ($latestArticles as $article): ?>
            <div class="col-md-6 mb-4 fade-in">
                <div class="article-card" data-article-id="<?php echo $article['id']; ?>">
                    <img src="<?php echo $article['featured_image'] ? SITE_URL . '/uploads/' . $article['featured_image'] : SITE_URL . '/assets/images/placeholder.jpg'; ?>"
                         alt="<?php echo htmlspecialchars($article['title']); ?>"
                         class="article-image"
                         loading="lazy">
                    <div class="article-content">
                        <div class="article-category-wrapper mb-2">
                            <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $article['category_slug']; ?>"
                               class="article-category">
                                <?php echo $article['category_name']; ?>
                            </a>
                        </div>
                        <h4 class="article-title">
                            <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                <?php echo htmlspecialchars($article['title']); ?>
                            </a>
                        </h4>
                        <p class="article-excerpt">
                            <?php echo truncateText($article['excerpt'] ?: strip_tags($article['content']), 120); ?>
                        </p>
                        <div class="article-meta">
                            <div class="meta-left">
                                <span><i class="fas fa-user"></i> <?php echo $article['author_name']; ?></span>
                                <span><i class="fas fa-clock"></i> <?php echo timeAgo($article['created_at']); ?></span>
                            </div>
                            <div class="meta-right">
                                <span><i class="fas fa-eye"></i> <?php echo formatNumber($article['views']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Load More Button -->
        <div class="text-center mb-4">
            <button class="load-more-btn" id="loadMoreBtn">
                <i class="fas fa-plus"></i>
                تحميل المزيد
            </button>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Popular Articles Widget -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-fire"></i>
                الأكثر قراءة
            </div>
            <div class="widget-content">
                <ul class="widget-list">
                    <?php foreach ($popularArticles as $index => $article): ?>
                    <li class="popular-article-item">
                        <div class="popular-article-number"><?php echo $index + 1; ?></div>
                        <div class="popular-article-content">
                            <h6 class="popular-article-title">
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                    <?php echo htmlspecialchars($article['title']); ?>
                                </a>
                            </h6>
                            <div class="popular-article-meta">
                                <span><i class="fas fa-eye"></i> <?php echo formatNumber($article['views']); ?></span>
                                <span><i class="fas fa-clock"></i> <?php echo timeAgo($article['created_at']); ?></span>
                            </div>
                        </div>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>

        <!-- Categories Widget -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-list"></i>
                الأقسام
            </div>
            <div class="widget-content">
                <ul class="widget-list">
                    <?php foreach ($categories as $category): ?>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>">
                            <?php echo $category['name']; ?>
                            <span class="category-count">(<?php echo isset($category['article_count']) ? $category['article_count'] : 0; ?>)</span>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>

        <!-- Match Schedule Widget -->
        <?php if (!empty($upcomingMatches)): ?>
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-futbol"></i>
                المباريات القادمة
            </div>
            <div class="widget-content">
                <?php foreach ($upcomingMatches as $match): ?>
                <div class="match-item">
                    <div class="match-teams">
                        <?php echo $match['home_team']; ?> vs <?php echo $match['away_team']; ?>
                    </div>
                    <div class="match-info">
                        <div class="match-league"><?php echo $match['league']; ?></div>
                        <div class="match-time">
                            <?php echo date('d/m H:i', strtotime($match['match_date'])); ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <div class="text-center mt-3">
                    <a href="<?php echo SITE_URL; ?>/matches.php" class="btn btn-sm btn-outline-primary">
                        عرض جميع المباريات
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Weather Widget (Optional) -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-cloud-sun"></i>
                الطقس
            </div>
            <div class="widget-content text-center">
                <div class="weather-info">
                    <div class="weather-temp">25°</div>
                    <div class="weather-desc">مشمس</div>
                    <div class="weather-location">الرياض</div>
                </div>
            </div>
        </div>

        <!-- Newsletter Widget -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-envelope"></i>
                النشرة الإخبارية
            </div>
            <div class="widget-content">
                <p>اشترك في نشرتنا الإخبارية لتصلك آخر الأخبار</p>
                <form class="newsletter-form" id="sidebarNewsletterForm">
                    <div class="mb-3">
                        <input type="email" class="form-control" placeholder="بريدك الإلكتروني" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-paper-plane"></i>
                        اشتراك
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>



<?php include 'includes/footer.php'; ?>
