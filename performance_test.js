// Performance Test Script for Arabic News Website
// Run this in browser console to test performance

class PerformanceTest {
    constructor() {
        this.results = {};
        this.startTime = performance.now();
    }

    // Test page load performance
    testPageLoad() {
        const navigation = performance.getEntriesByType('navigation')[0];
        
        this.results.pageLoad = {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            totalTime: navigation.loadEventEnd - navigation.navigationStart,
            dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
            tcpConnection: navigation.connectEnd - navigation.connectStart,
            serverResponse: navigation.responseEnd - navigation.responseStart
        };
        
        console.log('Page Load Performance:', this.results.pageLoad);
        return this.results.pageLoad;
    }

    // Test CSS performance
    testCSSPerformance() {
        const stylesheets = document.styleSheets;
        let totalRules = 0;
        let totalSelectors = 0;
        
        for (let i = 0; i < stylesheets.length; i++) {
            try {
                const rules = stylesheets[i].cssRules || stylesheets[i].rules;
                if (rules) {
                    totalRules += rules.length;
                    for (let j = 0; j < rules.length; j++) {
                        if (rules[j].selectorText) {
                            totalSelectors += rules[j].selectorText.split(',').length;
                        }
                    }
                }
            } catch (e) {
                console.warn('Cannot access stylesheet:', stylesheets[i].href);
            }
        }
        
        this.results.css = {
            stylesheets: stylesheets.length,
            totalRules: totalRules,
            totalSelectors: totalSelectors
        };
        
        console.log('CSS Performance:', this.results.css);
        return this.results.css;
    }

    // Test image performance
    testImagePerformance() {
        const images = document.querySelectorAll('img');
        let loadedImages = 0;
        let totalSize = 0;
        
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    loadedImages++;
                    resolve({
                        src: img.src,
                        naturalWidth: img.naturalWidth,
                        naturalHeight: img.naturalHeight,
                        loaded: true
                    });
                } else {
                    img.onload = () => {
                        loadedImages++;
                        resolve({
                            src: img.src,
                            naturalWidth: img.naturalWidth,
                            naturalHeight: img.naturalHeight,
                            loaded: true
                        });
                    };
                    img.onerror = () => {
                        resolve({
                            src: img.src,
                            loaded: false,
                            error: true
                        });
                    };
                }
            });
        });
        
        Promise.all(imagePromises).then(results => {
            this.results.images = {
                total: images.length,
                loaded: loadedImages,
                failed: results.filter(r => r.error).length,
                details: results
            };
            
            console.log('Image Performance:', this.results.images);
        });
        
        return imagePromises;
    }

    // Test JavaScript performance
    testJavaScriptPerformance() {
        const scripts = document.querySelectorAll('script');
        const scriptResources = performance.getEntriesByType('resource')
            .filter(entry => entry.name.includes('.js'));
        
        this.results.javascript = {
            scriptTags: scripts.length,
            externalScripts: scriptResources.length,
            scriptLoadTimes: scriptResources.map(script => ({
                name: script.name,
                duration: script.duration,
                size: script.transferSize
            }))
        };
        
        console.log('JavaScript Performance:', this.results.javascript);
        return this.results.javascript;
    }

    // Test responsive design
    testResponsiveDesign() {
        const breakpoints = [
            { name: 'Mobile', width: 375 },
            { name: 'Tablet', width: 768 },
            { name: 'Desktop', width: 1200 },
            { name: 'Large Desktop', width: 1920 }
        ];
        
        const currentWidth = window.innerWidth;
        const currentBreakpoint = breakpoints.find(bp => currentWidth <= bp.width) || breakpoints[breakpoints.length - 1];
        
        this.results.responsive = {
            currentWidth: currentWidth,
            currentHeight: window.innerHeight,
            currentBreakpoint: currentBreakpoint.name,
            devicePixelRatio: window.devicePixelRatio,
            orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
        };
        
        console.log('Responsive Design:', this.results.responsive);
        return this.results.responsive;
    }

    // Test accessibility
    testAccessibility() {
        const issues = [];
        
        // Check for alt attributes on images
        const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
        if (imagesWithoutAlt.length > 0) {
            issues.push(`${imagesWithoutAlt.length} images missing alt attributes`);
        }
        
        // Check for heading hierarchy
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        let previousLevel = 0;
        headings.forEach(heading => {
            const level = parseInt(heading.tagName.charAt(1));
            if (level > previousLevel + 1) {
                issues.push(`Heading hierarchy issue: ${heading.tagName} follows h${previousLevel}`);
            }
            previousLevel = level;
        });
        
        // Check for form labels
        const inputsWithoutLabels = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        const labelsCount = document.querySelectorAll('label').length;
        if (inputsWithoutLabels.length > labelsCount) {
            issues.push('Some form inputs may be missing labels');
        }
        
        this.results.accessibility = {
            issues: issues,
            imagesWithAlt: document.querySelectorAll('img[alt]').length,
            totalImages: document.querySelectorAll('img').length,
            headingsCount: headings.length,
            formsCount: document.querySelectorAll('form').length
        };
        
        console.log('Accessibility Test:', this.results.accessibility);
        return this.results.accessibility;
    }

    // Test RTL support
    testRTLSupport() {
        const htmlDir = document.documentElement.dir;
        const htmlLang = document.documentElement.lang;
        const rtlElements = document.querySelectorAll('[dir="rtl"]');
        const arabicText = document.body.textContent.match(/[\u0600-\u06FF]/g);
        
        this.results.rtl = {
            htmlDirection: htmlDir,
            htmlLanguage: htmlLang,
            rtlElements: rtlElements.length,
            hasArabicText: arabicText ? arabicText.length > 0 : false,
            arabicCharCount: arabicText ? arabicText.length : 0
        };
        
        console.log('RTL Support:', this.results.rtl);
        return this.results.rtl;
    }

    // Run all tests
    async runAllTests() {
        console.log('🚀 Starting Performance Tests...');
        
        this.testPageLoad();
        this.testCSSPerformance();
        await this.testImagePerformance();
        this.testJavaScriptPerformance();
        this.testResponsiveDesign();
        this.testAccessibility();
        this.testRTLSupport();
        
        const endTime = performance.now();
        this.results.testDuration = endTime - this.startTime;
        
        console.log('✅ All tests completed in', this.results.testDuration.toFixed(2), 'ms');
        console.log('📊 Complete Results:', this.results);
        
        return this.results;
    }

    // Generate report
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            results: this.results,
            recommendations: this.getRecommendations()
        };
        
        console.log('📋 Performance Report:', report);
        return report;
    }

    // Get performance recommendations
    getRecommendations() {
        const recommendations = [];
        
        if (this.results.pageLoad && this.results.pageLoad.totalTime > 3000) {
            recommendations.push('Page load time is slow (>3s). Consider optimizing images and reducing HTTP requests.');
        }
        
        if (this.results.css && this.results.css.totalSelectors > 4000) {
            recommendations.push('High number of CSS selectors. Consider CSS optimization.');
        }
        
        if (this.results.images && this.results.images.failed > 0) {
            recommendations.push(`${this.results.images.failed} images failed to load. Check image URLs.`);
        }
        
        if (this.results.accessibility && this.results.accessibility.issues.length > 0) {
            recommendations.push('Accessibility issues found. Review and fix for better user experience.');
        }
        
        return recommendations;
    }
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
    window.PerformanceTest = PerformanceTest;
    
    // Run tests after page load
    if (document.readyState === 'complete') {
        const test = new PerformanceTest();
        test.runAllTests().then(() => {
            console.log('Performance testing completed. Use test.generateReport() for detailed report.');
        });
    } else {
        window.addEventListener('load', () => {
            const test = new PerformanceTest();
            test.runAllTests().then(() => {
                console.log('Performance testing completed. Use test.generateReport() for detailed report.');
            });
        });
    }
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceTest;
}
