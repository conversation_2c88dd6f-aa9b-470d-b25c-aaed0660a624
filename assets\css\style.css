/* Arabic News Website Styles - Professional Reference Design Implementation */
:root {
    --primary-color: #dc2626;
    --primary-dark: #b91c1c;
    --secondary-color: #000000;
    --accent-color: #ffffff;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --border-color: #dee2e6;
    --text-color: #212529;
    --text-light: #6c757d;
    --text-white: #ffffff;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 25px rgba(0, 0, 0, 0.12);
    --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --header-height: 140px;
    --gradient-primary: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    --gradient-dark: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    --gradient-red: linear-gradient(45deg, #dc2626, #ef4444);
    --news-red: #c41e3a;
    --news-blue: #1e40af;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --accent-color: #1f2937;
    --light-color: #374151;
    --border-color: #4b5563;
    --text-color: #f9fafb;
    --text-light: #d1d5db;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 4px 25px rgba(0, 0, 0, 0.4);
    --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.5);
}

/* Theme Transition */
* {
    transition: background-color var(--transition), color var(--transition), border-color var(--transition);
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    z-index: 1000;
    background: var(--primary-color);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.theme-toggle:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-hover);
}

.theme-toggle i {
    transition: var(--transition);
}

[data-theme="dark"] .theme-toggle .fa-moon {
    display: none;
}

[data-theme="dark"] .theme-toggle .fa-sun {
    display: inline;
}

[data-theme="light"] .theme-toggle .fa-sun {
    display: none;
}

[data-theme="light"] .theme-toggle .fa-moon {
    display: inline;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.7;
    color: var(--text-color);
    background-color: var(--light-color);
    direction: rtl;
    text-align: right;
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Breaking News Ticker - Enhanced Professional Design */
.breaking-news-ticker {
    background: var(--news-red);
    color: white;
    padding: 10px 0;
    overflow: hidden;
    position: relative;
    border-bottom: 3px solid var(--primary-dark);
    box-shadow: 0 4px 12px rgba(196, 30, 58, 0.3);
    z-index: 999;
}

.breaking-news-ticker::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        var(--news-red) 0%,
        #d32f2f 50%,
        var(--news-red) 100%);
    z-index: 1;
}

.breaking-news-ticker .container {
    position: relative;
    z-index: 2;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.breaking-label {
    background: var(--accent-color);
    color: var(--news-red);
    padding: 6px 18px;
    border-radius: 20px;
    font-weight: 900;
    font-size: 0.85rem;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    border: 2px solid var(--accent-color);
    position: relative;
    z-index: 3;
    min-width: 120px;
    text-align: center;
}

.breaking-label::before {
    content: '●';
    margin-left: 6px;
    color: var(--news-red);
    animation: pulse 1.2s infinite;
    font-size: 0.8rem;
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
}

.ticker-content {
    flex: 1;
    overflow: hidden;
    margin-right: 15px;
    position: relative;
    height: 35px;
    display: flex;
    align-items: center;
}

.ticker-text {
    display: flex;
    align-items: center;
    white-space: nowrap;
    animation: scroll-rtl 25s linear infinite;
    font-size: 0.95rem;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1.4;
}

.ticker-text a {
    color: white;
    text-decoration: none;
    margin: 0 30px;
    transition: var(--transition);
    font-weight: 600;
    position: relative;
}

.ticker-text a:hover {
    text-decoration: underline;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
    transform: translateY(-1px);
}

.ticker-text a::after {
    content: '|';
    position: absolute;
    left: -15px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 300;
}

.ticker-text a:first-child::after {
    display: none;
}

@keyframes scroll-rtl {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Pause animation on hover */
.breaking-news-ticker:hover .ticker-text {
    animation-play-state: paused;
}

/* Modern Header Styles - Professional Reference Design */
.main-header {
    background: var(--accent-color);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 4px solid var(--primary-color);
    width: 100%;
    margin: 0;
    padding: 0;
}

.top-bar {
    background: var(--secondary-color);
    color: var(--text-white);
    padding: 8px 0;
    font-size: 0.8rem;
    width: 100%;
    margin: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.date-time {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.date-time i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.social-link {
    color: var(--text-white);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    text-decoration: none;
    font-size: 0.85rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px) scale(1.1);
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.header-main {
    padding: 20px 0;
    background: var(--accent-color);
    width: 100%;
    margin: 0;
    border-bottom: 2px solid var(--border-color);
}

.logo-text {
    font-size: 2.8rem;
    font-weight: 900;
    color: var(--primary-color);
    text-decoration: none;
    text-shadow: 2px 2px 6px rgba(0,0,0,0.15);
    letter-spacing: -1px;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
}

.logo-text:hover {
    color: var(--primary-dark);
    transform: scale(1.03);
    text-shadow: 3px 3px 8px rgba(0,0,0,0.2);
}

.logo-img {
    max-height: 70px;
    width: auto;
    transition: var(--transition);
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
}

.logo-img:hover {
    transform: scale(1.05);
    filter: drop-shadow(3px 3px 6px rgba(0,0,0,0.15));
}

.search-form {
    max-width: 450px;
    margin: 0 auto;
    position: relative;
}

.search-input {
    border: 2px solid var(--border-color);
    border-left: none;
    padding: 14px 18px;
    font-size: 1rem;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    background: #ffffff;
    box-shadow: var(--shadow);
    transition: var(--transition);
    font-weight: 500;
    width: 100%;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.12), var(--shadow-lg);
    background: var(--accent-color);
    outline: none;
}

.search-input::placeholder {
    color: var(--text-light);
    font-weight: 400;
}

.btn-search {
    background: var(--gradient-primary);
    color: var(--text-white);
    border: 2px solid var(--primary-color);
    padding: 14px 20px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    transition: var(--transition);
    font-weight: 700;
    box-shadow: var(--shadow);
    font-size: 0.95rem;
}

.btn-search:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
}

.btn-search i {
    font-size: 1.1rem;
}

/* Advanced Search Modal */
.advanced-search-modal {
    background: rgba(0, 0, 0, 0.8);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.advanced-search-modal.show {
    display: flex;
}

.advanced-search-content {
    background: var(--accent-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-hover);
    position: relative;
}

.advanced-search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.advanced-search-title {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.close-advanced-search {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-advanced-search:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.search-filter-group {
    margin-bottom: 20px;
}

.search-filter-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color);
}

.search-filter-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--accent-color);
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
}

.search-filter-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.15);
    outline: none;
}

.search-filter-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--accent-color);
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
    cursor: pointer;
}

.search-filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.15);
    outline: none;
}

.date-range-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.search-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.btn-search-advanced {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-search-advanced:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-clear-filters {
    background: var(--light-color);
    color: var(--text-color);
    border: 2px solid var(--border-color);
    padding: 12px 25px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-clear-filters:hover {
    background: var(--border-color);
    border-color: var(--text-light);
}

/* Newsletter Subscription */
.newsletter-widget {
    background: var(--gradient-primary);
    color: white;
    padding: 25px;
    border-radius: var(--border-radius-lg);
    margin-bottom: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.newsletter-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.newsletter-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
}

.newsletter-description {
    margin-bottom: 20px;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.newsletter-form {
    display: flex;
    gap: 10px;
    position: relative;
    z-index: 2;
}

.newsletter-input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: var(--border-radius);
    background: rgba(255,255,255,0.1);
    color: white;
    font-size: 1rem;
    transition: var(--transition);
}

.newsletter-input::placeholder {
    color: rgba(255,255,255,0.7);
}

.newsletter-input:focus {
    border-color: white;
    background: rgba(255,255,255,0.2);
    outline: none;
}

.newsletter-submit {
    background: white;
    color: var(--primary-color);
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.newsletter-submit:hover {
    background: var(--light-color);
    transform: translateY(-1px);
}

/* Social Sharing Counters */
.social-sharing-widget {
    background: var(--accent-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 30px;
}

.social-sharing-title {
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-align: center;
}

.social-sharing-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.social-share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    font-size: 0.9rem;
}

.social-share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: white;
    text-decoration: none;
}

.social-share-btn.facebook {
    background: #1877f2;
}

.social-share-btn.twitter {
    background: #1da1f2;
}

.social-share-btn.whatsapp {
    background: #25d366;
}

.social-share-btn.telegram {
    background: #0088cc;
}

.social-share-btn.linkedin {
    background: #0077b5;
}

.share-count {
    background: rgba(255,255,255,0.2);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 700;
}

/* Trending Articles Widget */
.trending-widget {
    background: var(--accent-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 30px;
}

.trending-title {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.trending-title i {
    color: #ff6b35;
    animation: pulse 2s infinite;
}

.trending-article {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.trending-article:last-child {
    border-bottom: none;
}

.trending-article:hover {
    background: var(--light-color);
    margin: 0 -10px;
    padding: 12px 10px;
    border-radius: var(--border-radius);
}

.trending-rank {
    background: var(--gradient-primary);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 700;
    flex-shrink: 0;
}

.trending-content {
    flex: 1;
}

.trending-article-title {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    line-height: 1.4;
    font-weight: 600;
}

.trending-article-title a {
    color: var(--text-color);
    text-decoration: none;
}

.trending-article-title a:hover {
    color: var(--primary-color);
}

.trending-meta {
    font-size: 0.8rem;
    color: var(--text-light);
    display: flex;
    gap: 10px;
}

.trending-views {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--primary-color);
    font-weight: 600;
}

/* Bookmark System */
.bookmark-btn {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 3;
    box-shadow: var(--shadow);
}

.bookmark-btn:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.bookmark-btn i {
    color: var(--text-light);
    font-size: 1.1rem;
    transition: var(--transition);
}

.bookmark-btn.bookmarked i {
    color: var(--primary-color);
}

.bookmark-btn.bookmarked {
    background: white;
}

/* Reading Progress Indicator */
.reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(220, 38, 38, 0.1);
    z-index: 1001;
    display: none;
}

.reading-progress.show {
    display: block;
}

.reading-progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.1s ease;
    border-radius: 0 2px 2px 0;
}

/* Infinite Scroll Loading */
.infinite-scroll-loading {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-light);
}

.infinite-scroll-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.infinite-scroll-text {
    font-size: 1rem;
    font-weight: 500;
}

/* Loading Skeletons */
.skeleton {
    background: linear-gradient(90deg, var(--border-color) 25%, rgba(220, 38, 38, 0.1) 50%, var(--border-color) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-article-card {
    background: var(--accent-color);
    border-radius: var(--border-radius);
    padding: 0;
    overflow: hidden;
    margin-bottom: 20px;
}

.skeleton-image {
    height: 220px;
    background: var(--border-color);
}

.skeleton-content {
    padding: 20px;
}

.skeleton-title {
    height: 20px;
    background: var(--border-color);
    border-radius: 4px;
    margin-bottom: 10px;
}

.skeleton-title.short {
    width: 70%;
}

.skeleton-text {
    height: 14px;
    background: var(--border-color);
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-meta {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.skeleton-meta-item {
    height: 12px;
    width: 80px;
    background: var(--border-color);
    border-radius: 4px;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background: var(--light-color);
    padding: 15px 0;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.breadcrumb-item a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-color);
    font-weight: 600;
}

.breadcrumb-separator {
    color: var(--text-light);
    font-size: 0.8rem;
}

/* Related Articles Recommendation Engine */
.related-articles-enhanced {
    background: var(--accent-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-top: 30px;
}

.related-articles-title {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.related-articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.related-article-enhanced {
    background: var(--light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.related-article-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.related-article-image-enhanced {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.related-article-content-enhanced {
    padding: 15px;
}

.related-article-title-enhanced {
    margin: 0 0 10px 0;
    font-size: 1rem;
    line-height: 1.4;
    font-weight: 600;
}

.related-article-title-enhanced a {
    color: var(--text-color);
    text-decoration: none;
}

.related-article-title-enhanced a:hover {
    color: var(--primary-color);
}

.related-article-meta-enhanced {
    font-size: 0.85rem;
    color: var(--text-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.similarity-score {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Modern Navigation - Professional Reference Design */
.main-navigation {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-lg);
    width: 100%;
    margin: 0;
    padding: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.main-navigation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    z-index: 1;
}

.navbar {
    padding: 0;
    min-height: 55px;
    position: relative;
    z-index: 2;
}

.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.4);
    color: var(--text-white);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.1);
}

.navbar-toggler:hover {
    border-color: var(--accent-color);
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.nav-link {
    color: var(--text-white) !important;
    padding: 16px 24px !important;
    font-weight: 700;
    font-size: 0.95rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 3px solid transparent;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-radius: 0;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-white) !important;
    border-bottom-color: var(--accent-color);
    transform: translateY(-1px);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    border-bottom-color: var(--accent-color);
    box-shadow: inset 0 -3px 0 var(--accent-color);
    font-weight: 800;
}

.nav-link i {
    font-size: 1rem;
    margin-left: 4px;
}

/* Main Content - Professional Layout */
.main-content {
    padding: 40px 0;
    min-height: calc(100vh - 350px);
    background: var(--light-color);
}

.container {
    max-width: 1200px;
}

/* Section Headers - Enhanced Design */
.section-header {
    margin-bottom: 35px;
    position: relative;
}

.section-title {
    color: var(--primary-color);
    font-weight: 800;
    font-size: 2rem;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 12px;
    margin-bottom: 0;
    position: relative;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: -0.3px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    right: 0;
    width: 50px;
    height: 3px;
    background: var(--gradient-dark);
    border-radius: 2px;
}

.section-title::before {
    content: '';
    position: absolute;
    bottom: -6px;
    right: 60px;
    width: 25px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.section-title i {
    margin-left: 10px;
    color: var(--primary-color);
    font-size: 1.6rem;
}

/* Load More Button */
.load-more-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
    border: none;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.load-more-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    color: var(--text-white);
}

.load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Modern Article Cards - Professional Reference Design */
.article-card {
    background: var(--accent-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--border-color);
    position: relative;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.article-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: var(--transition);
    position: relative;
}

.article-card:hover .article-image {
    transform: scale(1.05);
}

.article-content {
    padding: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    height: calc(100% - 220px);
}

.article-category-wrapper {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 2;
}

.article-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.4;
    color: var(--text-color);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    display: block;
}

.article-title a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.article-excerpt {
    color: var(--text-light);
    margin-bottom: 15px;
    line-height: 1.6;
    font-size: 0.95rem;
    font-weight: 400;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: var(--text-light);
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
    margin-top: auto;
}

.meta-left, .meta-right {
    display: flex;
    gap: 12px;
    align-items: center;
}

.meta-left span, .meta-right span {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
}

.article-category {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 0.8rem;
    text-decoration: none;
    transition: var(--transition);
    font-weight: 700;
    box-shadow: var(--shadow);
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.article-category:hover {
    background: var(--primary-dark);
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
    text-decoration: none;
}

/* Modern Featured Article - Professional Reference Design */
.featured-article {
    position: relative;
    height: 450px;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 40px;
    box-shadow: var(--shadow-hover);
    border: 2px solid var(--border-color);
    transition: var(--transition);
}

.featured-article:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.18);
}

.featured-article .article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.featured-article:hover .article-image {
    transform: scale(1.03);
}

.featured-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
    color: var(--text-white);
    padding: 50px 30px 30px;
}

.featured-category {
    margin-bottom: 12px;
}

.featured-category .article-category {
    font-size: 0.85rem;
    padding: 8px 16px;
    font-weight: 800;
}

.featured-title {
    font-size: 2.2rem;
    font-weight: 800;
    margin-bottom: 12px;
    line-height: 1.25;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.8);
    letter-spacing: -0.5px;
}

.featured-title a {
    color: var(--text-white);
    text-decoration: none;
    transition: var(--transition);
}

.featured-title a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* Modern Sidebar */
.sidebar-widget {
    background: var(--accent-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: 35px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.sidebar-widget:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.widget-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 20px 25px;
    font-weight: 700;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.widget-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-dark);
}

.widget-header i {
    font-size: 1.3rem;
}

.widget-content {
    padding: 25px;
}

.widget-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.widget-list li {
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.widget-list li:last-child {
    border-bottom: none;
}

.widget-list li:hover {
    background: var(--light-color);
    padding: 15px 15px;
    margin: 0 -15px;
    transform: translateX(5px);
}

.widget-list a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 600;
    font-size: 1.05rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 1.5;
}

.widget-list a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* Popular Articles Widget */
.popular-article-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.popular-article-item:last-child {
    border-bottom: none;
}

.popular-article-item:hover {
    background: var(--light-color);
    margin: 0 -25px;
    padding: 20px 25px;
    transform: translateX(5px);
}

.popular-article-number {
    background: var(--gradient-primary);
    color: var(--text-white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.1rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--accent-color);
    position: relative;
}

.popular-article-number::before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: var(--gradient-primary);
    z-index: -1;
}

.popular-article-content {
    flex: 1;
}

.popular-article-title {
    margin: 0 0 12px 0;
    font-size: 1.1rem;
    line-height: 1.5;
    font-weight: 700;
}

.popular-article-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    display: block;
}

.popular-article-title a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.popular-article-meta {
    font-size: 0.9rem;
    color: var(--text-light);
    display: flex;
    gap: 18px;
    font-weight: 500;
}

.popular-article-meta span {
    display: flex;
    align-items: center;
    gap: 6px;
}

.popular-article-meta i {
    color: var(--primary-color);
}

/* Category Count */
.category-count {
    background: var(--gradient-primary);
    color: var(--text-white);
    font-size: 0.8rem;
    font-weight: 700;
    padding: 4px 10px;
    border-radius: 15px;
    min-width: 25px;
    text-align: center;
    box-shadow: var(--shadow);
}

/* Weather Widget */
.weather-info {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: var(--border-radius-lg);
    color: var(--text-white);
    margin: -25px;
    margin-bottom: 0;
}

.weather-temp {
    font-size: 4rem;
    font-weight: 800;
    color: var(--text-white);
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    line-height: 1;
}

.weather-desc {
    font-size: 1.4rem;
    margin-bottom: 10px;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.weather-location {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.weather-location i {
    font-size: 1.2rem;
}

/* Match Schedule Widget */
.match-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    margin-bottom: 15px;
    background: var(--accent-color);
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.match-item:hover {
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.match-teams {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-color);
}

.match-info {
    text-align: left;
}

.match-league {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 5px;
}

.match-time {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary-color);
    background: var(--light-color);
    padding: 5px 10px;
    border-radius: var(--border-radius);
    display: inline-block;
}

.match-score {
    font-size: 1.4rem;
    font-weight: 800;
    color: var(--primary-color);
    background: var(--light-color);
    padding: 8px 15px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
}

.match-time {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Newsletter Form Styles */
.newsletter-form {
    margin-top: 20px;
}

.newsletter-form .form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 15px 20px;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
    background: var(--light-color);
    margin-bottom: 15px;
}

.newsletter-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(220, 38, 38, 0.15);
    background: var(--accent-color);
    outline: none;
}

.newsletter-form .btn {
    background: var(--gradient-primary);
    border: none;
    color: var(--text-white);
    padding: 15px 25px;
    font-size: 1.1rem;
    font-weight: 700;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.newsletter-form .btn:hover {
    background: var(--gradient-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    color: var(--text-white);
}

/* Modern Footer */
.main-footer {
    background: var(--secondary-color);
    color: var(--text-white);
    margin-top: 60px;
    border-top: 4px solid var(--primary-color);
}

.footer-top {
    padding: 60px 0 40px;
}

.widget-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 25px;
    color: var(--text-white);
    position: relative;
    padding-bottom: 10px;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 2px;
    background: var(--primary-color);
}

.widget-text {
    color: #d1d5db;
    line-height: 1.7;
    margin-bottom: 25px;
    font-size: 0.95rem;
}

.footer-links {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: #d1d5db;
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-links a:hover {
    color: var(--text-white);
    padding-right: 5px;
}

.newsletter-form .input-group {
    margin-top: 15px;
}

.newsletter-form .form-control {
    border: 1px solid #4b5563;
    background: #374151;
    color: white;
}

.newsletter-form .form-control:focus {
    border-color: var(--primary-color);
    background: #374151;
    color: white;
    box-shadow: none;
}

.footer-bottom {
    border-top: 1px solid #4b5563;
    padding: 20px 0;
}

.copyright {
    margin: 0;
    color: #d1d5db;
}

.footer-menu a {
    color: #d1d5db;
    text-decoration: none;
    margin: 0 10px;
    transition: var(--transition);
}

.footer-menu a:hover {
    color: white;
}

.separator {
    color: #6b7280;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: #1d4ed8;
    transform: translateY(-3px);
}

/* Modern Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 12px 24px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-white);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

.btn-primary:hover {
    background: var(--secondary-color);
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-1px);
}

/* Load More Button */
#loadMoreBtn {
    background: var(--gradient-primary);
    border: 2px solid var(--primary-color);
    color: var(--text-white);
    padding: 18px 45px;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

#loadMoreBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

#loadMoreBtn:hover::before {
    left: 100%;
}

#loadMoreBtn:hover {
    background: var(--gradient-dark);
    border-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
    color: var(--text-white);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: none;
    box-shadow: var(--shadow);
}

/* Enhanced Responsive Design */
@media (max-width: 992px) {
    .section-title {
        font-size: 1.8rem;
    }

    .featured-article {
        height: 400px;
    }

    .featured-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .header-main {
        text-align: center;
        padding: 15px 0;
    }

    .header-main .row > div {
        margin-bottom: 15px;
    }

    .search-form {
        max-width: 100%;
    }

    .featured-title {
        font-size: 1.6rem;
    }

    .featured-overlay {
        padding: 25px 20px 20px;
    }

    .featured-article {
        height: 350px;
    }

    .article-card {
        margin-bottom: 20px;
    }

    .article-image {
        height: 200px;
    }

    .breaking-news-ticker {
        font-size: 0.9rem;
        padding: 8px 0;
    }

    .ticker-text {
        animation-duration: 20s;
        font-size: 0.9rem;
    }

    .breaking-label {
        font-size: 0.8rem;
        padding: 5px 15px;
    }

    .nav-link {
        padding: 14px 20px !important;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.6rem;
    }
}

@media (max-width: 576px) {
    .top-bar {
        text-align: center;
        padding: 6px 0;
    }

    .date-time {
        justify-content: center;
        margin-bottom: 8px;
        font-size: 0.75rem;
    }

    .social-links {
        justify-content: center;
    }

    .social-link {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .logo-text {
        font-size: 2rem;
    }

    .nav-link {
        padding: 12px 15px !important;
        font-size: 0.85rem;
    }

    .featured-article {
        height: 300px;
    }

    .featured-title {
        font-size: 1.4rem;
    }

    .article-title {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.4rem;
    }

    .breaking-news-ticker {
        padding: 6px 0;
    }

    .ticker-text {
        font-size: 0.85rem;
    }

    .article-full {
        padding: 20px;
    }

    .article-title-full {
        font-size: 1.8rem;
    }

    .article-meta-full {
        flex-direction: column;
        gap: 10px;
    }

    .category-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }

    .category-header {
        padding: 20px;
    }

    .sharing-buttons {
        justify-content: center;
    }

    .share-btn, .btn-copy {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}

/* Additional Modern Features */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Performance Optimizations */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* GPU Acceleration for Animations */
.article-card,
.featured-article,
.breaking-news-ticker,
.nav-link,
.btn,
.social-link {
    will-change: transform;
    transform: translateZ(0);
}

/* Optimize Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

.article-image,
.article-image-full,
.related-article-image {
    object-fit: cover;
    object-position: center;
}

/* Focus States for Accessibility */
.nav-link:focus,
.btn:focus,
.search-input:focus,
.social-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Article Page Styles */
.article-full {
    background: var(--accent-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.article-title-full {
    font-size: 2.2rem;
    font-weight: 800;
    line-height: 1.3;
    color: var(--text-color);
    margin-bottom: 20px;
}

.article-meta-full {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 15px 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-light);
    margin-bottom: 25px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.meta-item i {
    color: var(--primary-color);
}

.breaking-badge {
    background: var(--news-red);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    margin-right: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.article-image-wrapper {
    text-align: center;
    margin-bottom: 25px;
}

.article-image-full {
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.article-content-full {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color);
}

.article-content-full p {
    margin-bottom: 20px;
}

.article-content-full h2,
.article-content-full h3,
.article-content-full h4 {
    color: var(--primary-color);
    margin: 25px 0 15px 0;
    font-weight: 700;
}

.article-tags {
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
}

.article-tags h6 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-weight: 700;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-link {
    background: var(--light-color);
    color: var(--text-color);
    padding: 6px 14px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.85rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    font-weight: 500;
}

.tag-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.article-sharing {
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
}

.article-sharing h6 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-weight: 700;
}

.sharing-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.share-btn, .btn-copy {
    padding: 10px 18px;
    border: none;
    border-radius: var(--border-radius);
    color: white;
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
}

.btn-facebook { background: #1877f2; }
.btn-twitter { background: #1da1f2; }
.btn-whatsapp { background: #25d366; }
.btn-telegram { background: #0088cc; }
.btn-copy { background: var(--text-light); }

.share-btn:hover, .btn-copy:hover {
    transform: translateY(-2px);
    opacity: 0.9;
    box-shadow: var(--shadow);
}

/* Related Articles */
.related-articles {
    margin-top: 40px;
}

.related-article-card {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.related-article-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.related-article-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    flex-shrink: 0;
}

.related-article-content {
    flex: 1;
}

.related-article-title {
    margin: 0 0 8px 0;
    font-size: 0.95rem;
    line-height: 1.4;
    font-weight: 600;
}

.related-article-title a {
    color: var(--text-color);
    text-decoration: none;
}

.related-article-title a:hover {
    color: var(--primary-color);
}

.related-article-meta {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Comments Section */
.comments-section {
    background: var(--accent-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-top: 30px;
}

.comment-form-wrapper {
    background: var(--light-color);
    padding: 25px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
}

.comment-form .form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 1rem;
    transition: var(--transition);
}

.comment-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.15);
}

.comment-item {
    background: var(--light-color);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    border-left: 4px solid var(--primary-color);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-author {
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.comment-author i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.comment-date {
    font-size: 0.85rem;
    color: var(--text-light);
}

.comment-content {
    color: var(--text-color);
    line-height: 1.6;
}

.no-comments {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-light);
}

.no-comments i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--border-color);
}

/* Category Page Styles */
.category-header {
    background: var(--accent-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    text-align: center;
    border-top: 4px solid var(--primary-color);
}

.category-title {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.category-title i {
    color: var(--primary-color);
}

.category-description {
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 20px;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.category-meta {
    color: var(--text-light);
    font-size: 1rem;
}

.articles-count {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--light-color);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
}

.articles-count i {
    color: var(--primary-color);
}

.articles-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 40px;
}

.no-articles {
    text-align: center;
    padding: 60px 30px;
    background: var(--accent-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.no-articles-icon {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.no-articles-icon i {
    color: var(--border-color);
}

.no-articles h3 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-weight: 700;
}

.no-articles p {
    color: var(--text-light);
    margin-bottom: 25px;
    line-height: 1.6;
}

/* Pagination Styles */
.pagination-nav {
    margin: 40px 0;
}

.pagination .page-link {
    color: var(--text-color);
    background: var(--accent-color);
    border: 1px solid var(--border-color);
    padding: 10px 15px;
    margin: 0 2px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.pagination .page-link:hover {
    color: var(--text-white);
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    color: var(--text-white);
    background: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.pagination .page-item.disabled .page-link {
    color: var(--text-light);
    background: var(--light-color);
    border-color: var(--border-color);
    cursor: not-allowed;
}

/* Sidebar Widgets for Category Page */
.categories-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.categories-list li {
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.categories-list li:last-child {
    border-bottom: none;
}

.categories-list a {
    color: var(--text-color);
    text-decoration: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
    font-weight: 500;
}

.categories-list a:hover {
    color: var(--primary-color);
    padding-right: 5px;
}

.category-count {
    color: var(--text-light);
    font-size: 0.85rem;
    background: var(--light-color);
    padding: 2px 8px;
    border-radius: 10px;
}

.popular-articles {
    list-style: none;
    margin: 0;
    padding: 0;
}

.popular-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-rank {
    background: var(--gradient-primary);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: var(--shadow);
}

.popular-content {
    flex: 1;
}

.popular-content h6 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    line-height: 1.4;
    font-weight: 600;
}

.popular-content a {
    color: var(--text-color);
    text-decoration: none;
}

.popular-content a:hover {
    color: var(--primary-color);
}

.popular-meta {
    font-size: 0.8rem;
    color: var(--text-light);
    display: flex;
    gap: 10px;
}

.popular-meta span {
    font-weight: 500;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--accent-color);
    color: var(--text-color);
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 9999;
    transform: translateX(-100%);
    opacity: 0;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
    min-width: 300px;
    max-width: 400px;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-success {
    border-left-color: #059669;
}

.toast-error {
    border-left-color: #dc2626;
}

.toast-info {
    border-left-color: #2563eb;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast-content i {
    font-size: 1.2rem;
}

.toast-success .toast-content i {
    color: #059669;
}

.toast-error .toast-content i {
    color: #dc2626;
}

.toast-info .toast-content i {
    color: #2563eb;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced Form Styles */
.form-control.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.15);
}

.form-control.is-valid {
    border-color: #059669;
    box-shadow: 0 0 0 0.2rem rgba(5, 150, 105, 0.15);
}

/* Print Styles */
@media print {
    .breaking-news-ticker,
    .main-navigation,
    .sidebar,
    .main-footer,
    .scroll-to-top {
        display: none !important;
    }

    .main-content {
        padding: 0;
    }

    .article-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .featured-article {
        break-inside: avoid;
    }
}
