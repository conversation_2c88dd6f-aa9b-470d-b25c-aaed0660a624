/* Admin Panel Styles - Enhanced Design */
:root {
    --admin-primary: #dc2626;
    --admin-primary-dark: #b91c1c;
    --admin-secondary: #64748b;
    --admin-success: #059669;
    --admin-warning: #f59e0b;
    --admin-danger: #dc2626;
    --admin-dark: #1f2937;
    --admin-light: #f8fafc;
    --admin-border: #e5e7eb;
    --admin-sidebar-width: 280px;
    --admin-header-height: 70px;
    --admin-gradient-primary: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    --admin-gradient-dark: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    --admin-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    --admin-shadow-lg: 0 4px 25px rgba(0, 0, 0, 0.12);
    --admin-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
    --admin-border-radius: 8px;
    --admin-border-radius-lg: 12px;
    --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-body {
    background: var(--admin-light);
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
}

/* Admin Header - Enhanced */
.admin-header {
    background: var(--admin-gradient-dark);
    color: white;
    height: var(--admin-header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--admin-shadow-lg);
    border-bottom: 3px solid var(--admin-primary);
}

.admin-header .container-fluid {
    height: 100%;
}

.admin-header .row {
    height: 100%;
}

.admin-logo a {
    color: white;
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-logo a:hover {
    color: #e5e7eb;
}

.admin-search {
    max-width: 400px;
    margin: 0 auto;
}

.admin-search .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.admin-search .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--admin-primary);
    color: white;
    box-shadow: none;
}

.admin-search .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* Admin Sidebar - Enhanced */
.admin-sidebar {
    background: white;
    width: var(--admin-sidebar-width);
    height: calc(100vh - var(--admin-header-height));
    position: fixed;
    top: var(--admin-header-height);
    right: 0;
    overflow-y: auto;
    box-shadow: var(--admin-shadow-lg);
    transition: var(--admin-transition);
    z-index: 999;
    border-left: 1px solid var(--admin-border);
}

.admin-sidebar.collapsed {
    transform: translateX(100%);
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: var(--admin-secondary);
    text-decoration: none;
    transition: var(--admin-transition);
    border-right: 3px solid transparent;
    border-radius: 0 var(--admin-border-radius) var(--admin-border-radius) 0;
    margin: 2px 0;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: var(--admin-gradient-primary);
    transition: var(--admin-transition);
    z-index: -1;
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link:hover {
    color: white;
    border-right-color: var(--admin-primary);
    transform: translateX(-5px);
}

.nav-link.active {
    background: var(--admin-gradient-primary);
    color: white;
    border-right-color: var(--admin-primary-dark);
    font-weight: 600;
    box-shadow: var(--admin-shadow);
}

.nav-link i {
    width: 20px;
    margin-left: 12px;
    text-align: center;
}

.nav-link .badge {
    margin-right: auto;
    margin-left: 10px;
}

.submenu-arrow {
    margin-right: auto;
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.nav-link[aria-expanded="true"] .submenu-arrow {
    transform: rotate(180deg);
}

.submenu {
    background: var(--admin-light);
    border-right: 3px solid var(--admin-border);
    margin: 0;
    padding: 0;
    list-style: none;
}

.submenu-link {
    display: flex;
    align-items: center;
    padding: 10px 25px 10px 50px;
    color: var(--admin-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.submenu-link:hover {
    background: white;
    color: var(--admin-primary);
}

.submenu-link i {
    width: 16px;
    margin-left: 10px;
    text-align: center;
}

/* Main Content */
.admin-main {
    margin-right: var(--admin-sidebar-width);
    margin-top: var(--admin-header-height);
    min-height: calc(100vh - var(--admin-header-height));
    padding: 20px;
    transition: margin-right 0.3s ease;
}

.admin-main.expanded {
    margin-right: 0;
}

.admin-breadcrumb {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.breadcrumb {
    margin: 0;
}

.breadcrumb-item a {
    color: var(--admin-primary);
    text-decoration: none;
}

.page-header {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-dark);
    margin: 0;
}

.page-description {
    color: var(--admin-secondary);
    margin: 10px 0 0 0;
    font-size: 1.1rem;
}

/* Admin Cards - Enhanced */
.admin-card {
    background: white;
    border-radius: var(--admin-border-radius-lg);
    box-shadow: var(--admin-shadow);
    overflow: hidden;
    margin-bottom: 25px;
    transition: var(--admin-transition);
    border: 1px solid var(--admin-border);
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-hover);
}

.admin-card-header {
    background: var(--admin-gradient-primary);
    padding: 20px 25px;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.admin-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.admin-card-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
    margin: 0;
    position: relative;
    z-index: 2;
}

.admin-card-body {
    padding: 25px;
    position: relative;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid var(--admin-border);
    border-radius: 6px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
}

.form-control.is-invalid {
    border-color: var(--admin-danger);
}

.form-control.is-valid {
    border-color: var(--admin-success);
}

.invalid-feedback {
    color: var(--admin-danger);
    font-size: 0.875rem;
    margin-top: 5px;
}

.valid-feedback {
    color: var(--admin-success);
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Buttons - Enhanced */
.btn {
    padding: 12px 24px;
    border-radius: var(--admin-border-radius);
    font-weight: 600;
    transition: var(--admin-transition);
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--admin-gradient-primary);
    color: white;
    box-shadow: var(--admin-shadow);
}

.btn-primary:hover {
    background: var(--admin-primary-dark);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-hover);
}

.btn-success {
    background: var(--admin-success);
    color: white;
}

.btn-success:hover {
    background: #047857;
    color: white;
}

.btn-warning {
    background: var(--admin-warning);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
    color: white;
}

.btn-danger {
    background: var(--admin-danger);
    color: white;
}

.btn-danger:hover {
    background: #b91c1c;
    color: white;
}

.btn-secondary {
    background: var(--admin-secondary);
    color: white;
}

.btn-secondary:hover {
    background: #475569;
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--admin-primary);
    border: 2px solid var(--admin-primary);
}

.btn-outline-primary:hover {
    background: var(--admin-primary);
    color: white;
}

/* Tables */
.table-responsive {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table {
    margin: 0;
}

.table th {
    background: var(--admin-light);
    border: none;
    font-weight: 600;
    color: var(--admin-dark);
    padding: 15px;
    border-bottom: 2px solid var(--admin-border);
}

.table td {
    padding: 15px;
    border-bottom: 1px solid var(--admin-border);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(30, 64, 175, 0.05);
}

/* Pagination */
.pagination {
    justify-content: center;
    margin-top: 25px;
}

.page-link {
    color: var(--admin-primary);
    border: 1px solid var(--admin-border);
    padding: 10px 15px;
}

.page-link:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

.page-item.active .page-link {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
}

/* Admin Footer */
.admin-footer {
    background: white;
    border-top: 1px solid var(--admin-border);
    padding: 20px 0;
    margin-top: 40px;
}

.footer-text {
    margin: 0;
    color: var(--admin-secondary);
}

.footer-links a {
    color: var(--admin-secondary);
    text-decoration: none;
    margin: 0 10px;
}

.footer-links a:hover {
    color: var(--admin-primary);
}

.separator {
    color: var(--admin-border);
}

/* Editor Toolbar */
.editor-toolbar {
    background: var(--admin-light);
    border: 2px solid var(--admin-border);
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    padding: 10px;
    display: flex;
    gap: 5px;
}

.editor-toolbar button {
    background: white;
    border: 1px solid var(--admin-border);
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.editor-toolbar button:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

.editor-toolbar + textarea {
    border-radius: 0 0 6px 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
    }
    
    .admin-main {
        margin-right: 0;
    }
    
    .admin-header .col-md-6 {
        display: none;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .admin-card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .admin-main {
        padding: 15px;
    }
    
    .page-header {
        padding: 20px;
    }
    
    .admin-card-header,
    .admin-card-body {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    z-index: 1050;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-right: 4px solid var(--admin-success);
}

.toast-error {
    border-right: 4px solid var(--admin-danger);
}

.toast-warning {
    border-right: 4px solid var(--admin-warning);
}

.toast-info {
    border-right: 4px solid var(--admin-primary);
}
